// Dashboard layout component

interface DashboardLayoutProps {
  children: any;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="min-h-screen text-white bg-[url('https://thecave.ams3.cdn.digitaloceanspaces.com/backgroundapp.jpeg')] bg-cover bg-center bg-fixed">
      {/* Main content with padding for the fixed navbar */}
      <main className="container mx-auto p-6 pt-24">
        {children}
      </main>
    </div>
  );
}
