import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const meterTestnet = /*#__PURE__*/ defineChain({
    id: 83,
    name: 'Meter Testnet',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: '<PERSON><PERSON>',
    },
    rpcUrls: {
        default: { http: ['https://rpctest.meter.io'] },
    },
    blockExplorers: {
        default: {
            name: 'MeterTestnetScan',
            url: 'https://scan-warringstakes.meter.io',
        },
    },
});
//# sourceMappingURL=meterTestnet.js.map