{"version": 3, "file": "serializers.js", "sourceRoot": "", "sources": ["../../celo/serializers.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAA;AACnD,OAAO,EAAE,mBAAmB,EAAE,MAAM,sBAAsB,CAAA;AAC1D,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAA;AAC7C,OAAO,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAA;AACxD,OAAO,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAA;AAC3E,OAAO,EAAE,oBAAoB,IAAI,uBAAuB,EAAE,MAAM,4BAA4B,CAAA;AAG5F,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAA;AACzD,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAA;AACnD,OAAO,EAAE,KAAK,EAAE,MAAM,4BAA4B,CAAA;AAClD,OAAO,EAAE,KAAK,EAAE,MAAM,4BAA4B,CAAA;AAClD,OAAO,EAAE,mBAAmB,EAAE,MAAM,6CAA6C,CAAA;AACjF,OAAO,EAAE,uBAAuB,EAAE,MAAM,8CAA8C,CAAA;AAOtF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAA;AAExD,MAAM,UAAU,oBAAoB,CAClC,WAAwC,EACxC,SAAiC;IAEjC,IAAI,OAAO,CAAC,WAAW,CAAC;QACtB,OAAO,yBAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAA;IAC1D,OAAO,uBAAuB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAA;AACxD,CAAC;AAED,MAAM,CAAC,MAAM,WAAW,GAAG;IACzB,WAAW,EAAE,oBAAoB;CACE,CAAA;AAOrC,SAAS,yBAAyB,CAChC,WAAyC,EACzC,SAAiC;IAEjC,sBAAsB,CAAC,WAAW,CAAC,CAAA;IACnC,MAAM,EACJ,OAAO,EACP,GAAG,EACH,KAAK,EACL,EAAE,EACF,KAAK,EACL,YAAY,EACZ,oBAAoB,EACpB,UAAU,EACV,WAAW,EACX,IAAI,GACL,GAAG,WAAW,CAAA;IAEf,MAAM,qBAAqB,GAAG;QAC5B,KAAK,CAAC,OAAO,CAAC;QACd,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QAC3B,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI;QACzD,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;QACzC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;QACvB,EAAE,IAAI,IAAI;QACV,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QAC3B,IAAI,IAAI,IAAI;QACZ,mBAAmB,CAAC,UAAU,CAAC;QAC/B,WAAY;QACZ,GAAG,uBAAuB,CAAC,WAAW,EAAE,SAAS,CAAC;KACnD,CAAA;IAED,OAAO,SAAS,CAAC;QACf,MAAM;QACN,KAAK,CAAC,qBAAqB,CAAC;KAC7B,CAAwC,CAAA;AAC3C,CAAC;AAED,4CAA4C;AAC5C,MAAM,mBAAmB,GAAG,UAAU,CAAA;AAEtC,MAAM,UAAU,sBAAsB,CACpC,WAAyC;IAEzC,MAAM,EACJ,OAAO,EACP,oBAAoB,EACpB,QAAQ,EACR,YAAY,EACZ,EAAE,EACF,WAAW,EACX,UAAU,EACV,mBAAmB,GACpB,GAAG,WAAW,CAAA;IACf,IAAI,OAAO,IAAI,CAAC;QAAE,MAAM,IAAI,mBAAmB,CAAC,EAAE,OAAO,EAAE,CAAC,CAAA;IAC5D,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QAAE,MAAM,IAAI,mBAAmB,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAA;IACxE,IAAI,QAAQ;QACV,MAAM,IAAI,SAAS,CACjB,yDAAyD,CAC1D,CAAA;IAEH,IAAI,SAAS,CAAC,YAAY,CAAC,IAAI,YAAY,GAAG,mBAAmB;QAC/D,MAAM,IAAI,kBAAkB,CAAC,EAAE,YAAY,EAAE,CAAC,CAAA;IAEhD,IACE,SAAS,CAAC,oBAAoB,CAAC;QAC/B,SAAS,CAAC,YAAY,CAAC;QACvB,oBAAoB,GAAG,YAAY;QAEnC,MAAM,IAAI,mBAAmB,CAAC,EAAE,YAAY,EAAE,oBAAoB,EAAE,CAAC,CAAA;IAEvE,IACE,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACvD,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,EACvD,CAAC;QACD,MAAM,IAAI,SAAS,CACjB,mEAAmE,CACpE,CAAA;IACH,CAAC;IAED,IAAI,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC;QACtD,MAAM,IAAI,SAAS,CACjB,gEAAgE,CACjE,CAAA;IACH,CAAC;IAED,IAAI,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,CAAC;QACtE,MAAM,IAAI,mBAAmB,CAAC,mBAAmB,CAAC,CAAA;IACpD,CAAC;IAED,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC;QACzD,MAAM,IAAI,SAAS,CACjB,yFAAyF,CAC1F,CAAA;IACH,CAAC;AACH,CAAC;AAED,MAAM,UAAU,sBAAsB,CACpC,WAAyC;IAEzC,MAAM,EACJ,OAAO,EACP,oBAAoB,EACpB,QAAQ,EACR,YAAY,EACZ,EAAE,EACF,WAAW,GACZ,GAAG,WAAW,CAAA;IAEf,IAAI,OAAO,IAAI,CAAC;QAAE,MAAM,IAAI,mBAAmB,CAAC,EAAE,OAAO,EAAE,CAAC,CAAA;IAC5D,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QAAE,MAAM,IAAI,mBAAmB,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAA;IAExE,IAAI,QAAQ;QACV,MAAM,IAAI,SAAS,CACjB,yDAAyD,CAC1D,CAAA;IAEH,IAAI,SAAS,CAAC,YAAY,CAAC,IAAI,YAAY,GAAG,mBAAmB;QAC/D,MAAM,IAAI,kBAAkB,CAAC,EAAE,YAAY,EAAE,CAAC,CAAA;IAChD,IACE,SAAS,CAAC,oBAAoB,CAAC;QAC/B,SAAS,CAAC,YAAY,CAAC;QACvB,oBAAoB,GAAG,YAAY;QAEnC,MAAM,IAAI,mBAAmB,CAAC,EAAE,YAAY,EAAE,oBAAoB,EAAE,CAAC,CAAA;IAEvE,IAAI,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC;QACtD,MAAM,IAAI,SAAS,CACjB,gEAAgE,CACjE,CAAA;IACH,CAAC;IAED,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;QACzB,MAAM,IAAI,SAAS,CACjB,yDAAyD,CAC1D,CAAA;IACH,CAAC;AACH,CAAC"}