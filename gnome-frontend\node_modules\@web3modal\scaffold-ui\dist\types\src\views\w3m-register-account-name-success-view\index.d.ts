import { LitElement } from 'lit';
export declare class W3mRegisterAccountNameSuccess extends LitElement {
    static styles: import("lit").CSSResult;
    render(): import("lit").TemplateResult<1>;
    private onboardingTemplate;
    private buttonsTemplate;
    private redirectToAccount;
}
declare global {
    interface HTMLElementTagNameMap {
        'w3m-register-account-name-success-view': W3mRegisterAccountNameSuccess;
    }
}
