import { useState, useEffect } from 'preact/hooks';
import miningApi from '../services/mining';
import stacksApi from '../services/stacks';

interface MiningData {
  minings: any[];
  stacks: {
    totalCopper: number;
    totalSilver: number;
    totalGold: number;
    estimatedCoin: number;
    estimatedCoinBonus: number;
  };
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useMiningData(): MiningData {
  const [minings, setMinings] = useState<any[]>([]);
  const [stacks, setStacks] = useState({
    totalCopper: 0,
    totalSilver: 0,
    totalGold: 0,
    estimatedCoin: 0,
    estimatedCoinBonus: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch mining operations
      const miningsData = await miningApi.getAllMinings();
      setMinings(miningsData);
      
      // Fetch stacks data
      const stacksData = await stacksApi.getStacks();
      setStacks(stacksData);
    } catch (error: any) {
      console.error('Error fetching mining data:', error);
      setError(error.message || 'Failed to fetch mining data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  return {
    minings,
    stacks,
    isLoading,
    error,
    refetch: fetchData
  };
}

export default useMiningData;
