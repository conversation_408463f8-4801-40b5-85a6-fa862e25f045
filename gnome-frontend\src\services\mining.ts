import api from './api';

// Mining API endpoints
export const miningApi = {
  // Get all mining operations
  getAllMinings: async () => {
    const response = await api.get('/mining');
    return response.data;
  },

  // Get a specific mining operation by ID
  getMining: async (miningId: string) => {
    const response = await api.get(`/mining/${miningId}`);
    return response.data;
  },

  // Start a mining operation
  startMining: async (miningId: string) => {
    const response = await api.post(`/mining/${miningId}/start`);
    return response.data;
  },

  // Stop a mining operation
  stopMining: async (miningId: string) => {
    const response = await api.post(`/mining/${miningId}/stop`);
    return response.data;
  },

  // Smelt a mining operation
  smeltMining: async (miningId: string) => {
    const response = await api.post(`/mining/${miningId}/smelt`);
    return response.data;
  },

  // Stack a mining operation
  stackMining: async (miningId: string) => {
    const response = await api.post(`/mining/${miningId}/stack`);
    return response.data;
  },

  // Smelt all stacked ores
  smeltAllStacks: async () => {
    const response = await api.post('/stacks/smelt-all');
    return response.data;
  },

  // Get smelting data
  getSmeltingData: async () => {
    const response = await api.get('/smelters');
    return response.data;
  },

  // Get global smelting stats
  getGlobalStats: async () => {
    const response = await api.get('/smelters/stats');
    return response.data;
  },

  // Claim tokens
  claimTokens: async (amount?: number) => {
    const response = await api.post('/smelters/claim', amount ? { amount } : {});
    return response.data;
  },
  
  // Stack all mining operations that are ready to smelt
  stackAllMinings: async () => {
    const response = await api.post('/stacks');
    return response.data;
  }
};

export default miningApi;
