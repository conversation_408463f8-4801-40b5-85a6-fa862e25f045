import copperImage from '../../assets/Copper.png';
import silverImage from '../../assets/Silver.png';
import goldImage from '../../assets/Gold.png';
import smeltIcon from '../../assets/smelticon.png';

interface SmeltingPanelProps {
  stacks: {
    totalCopper: number;
    totalSilver: number;
    totalGold: number;
  };
  smeltingActionLoading: boolean;
  onSmeltAllStacks: () => void;
}

export function SmeltingPanel({
  stacks,
  smeltingActionLoading,
  onSmeltAllStacks
}: SmeltingPanelProps) {
  const totalOres = stacks.totalCopper + stacks.totalSilver + stacks.totalGold;

  return (
    <div className="bg-[#222222] rounded-lg p-5 shadow-lg">
      <div className="flex items-center gap-2 mb-4">
        <img src={smeltIcon} alt="Smelt" className="w-6 h-6" />
        <h3 className="text-lg pixelated-text text-white">Smelting Operations</h3>
      </div>

      {/* Total stacked ores with progress bar */}
      <div className="mb-4 bg-[#191919] p-3 rounded-lg">
        <div className="flex justify-between items-center mb-2">
          <span className="pixelated-text text-white">Stacked Ores:</span>
          <span className="text-green-500 pixelated-text font-bold">{totalOres}</span>
        </div>
        
        {/* Progress bar showing ore distribution */}
        <div className="h-3 bg-[#111111] rounded-full overflow-hidden flex">
          <div 
            className="bg-orange-600 h-full" 
            style={{ width: `${(stacks.totalCopper / (totalOres || 1)) * 100}%` }}
          ></div>
          <div 
            className="bg-gray-400 h-full" 
            style={{ width: `${(stacks.totalSilver / (totalOres || 1)) * 100}%` }}
          ></div>
          <div 
            className="bg-yellow-500 h-full" 
            style={{ width: `${(stacks.totalGold / (totalOres || 1)) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Ore breakdown with improved visuals */}
      <div className="grid grid-cols-3 gap-2 mb-4">
        {/* Copper */}
        <div className="bg-[#191919] p-2 rounded-lg flex flex-col items-center">
          <div className="flex items-center justify-center mb-1">
            <img src={copperImage} alt="Copper" className="w-6 h-6" />
          </div>
          <span className="pixelated-text text-xs text-orange-400">Copper</span>
          <span className="pixelated-text text-green-500 font-bold">{stacks.totalCopper}</span>
        </div>
        
        {/* Silver */}
        <div className="bg-[#191919] p-2 rounded-lg flex flex-col items-center">
          <div className="flex items-center justify-center mb-1">
            <img src={silverImage} alt="Silver" className="w-6 h-6" />
          </div>
          <span className="pixelated-text text-xs text-gray-300">Silver</span>
          <span className="pixelated-text text-green-500 font-bold">{stacks.totalSilver}</span>
        </div>
        
        {/* Gold */}
        <div className="bg-[#191919] p-2 rounded-lg flex flex-col items-center">
          <div className="flex items-center justify-center mb-1">
            <img src={goldImage} alt="Gold" className="w-6 h-6" />
          </div>
          <span className="pixelated-text text-xs text-yellow-400">Gold</span>
          <span className="pixelated-text text-green-500 font-bold">{stacks.totalGold}</span>
        </div>
      </div>

      <button
        className={`w-full pixelated-text py-2 px-4 rounded mb-4 ${totalOres === 0 ? 'bg-gray-700 text-gray-400 cursor-not-allowed' : 'bg-orange-600 hover:bg-orange-700 text-white'}`}
        onClick={onSmeltAllStacks}
        disabled={smeltingActionLoading || totalOres === 0}
      >
        {smeltingActionLoading ? 'Processing...' : 'Smelt All Stacked Ores'}
      </button>

      <div className="bg-[#191919] p-3 rounded-lg">
        <p className="text-xs pixelated-text text-gray-400">
          Smelting converts your raw ores into coins that can be claimed as $CAVE.
        </p>
      </div>
    </div>
  );
}
