import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const mantaTestnet = /*#__PURE__*/ define<PERSON>hain({
    id: 3_441_005,
    name: 'Manta Pacific Testnet',
    network: 'manta-testnet',
    nativeCurrency: {
        decimals: 18,
        name: 'ETH',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: { http: ['https://manta-testnet.calderachain.xyz/http'] },
    },
    blockExplorers: {
        default: {
            name: 'Manta Testnet Explorer',
            url: 'https://pacific-explorer.testnet.manta.network',
            apiUrl: 'https://pacific-explorer.testnet.manta.network/api',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 419915,
        },
    },
    testnet: true,
});
//# sourceMappingURL=mantaTestnet.js.map