/**
 * Simple and universal wallet detection utilities
 */

/**
 * Detect if we're on a mobile device
 */
export const isMobile = (): boolean => {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false;
  }

  const userAgent = navigator.userAgent || '';
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
};

/**
 * Detect available wallet providers
 */
export const getAvailableWallets = () => {
  if (typeof window === 'undefined') return [];

  const wallets = [];

  // Check for injected providers
  if (window.ethereum) {
    // MetaMask
    if (window.ethereum.isMetaMask) {
      wallets.push({ name: 'MetaMask', provider: window.ethereum, type: 'injected' });
    }
    // Coinbase Wallet
    else if (window.ethereum.isCoinbaseWallet) {
      wallets.push({ name: 'Coinbase Wallet', provider: window.ethereum, type: 'injected' });
    }
    // Generic injected wallet
    else {
      wallets.push({ name: 'Injected Wallet', provider: window.ethereum, type: 'injected' });
    }
  }

  return wallets;
};

/**
 * Simple error handler for wallet operations
 */
export const handleWalletError = (error: any): string => {
  if (!error) return 'Unknown error occurred';

  const errorMessage = error.message || error.toString();

  // User rejection
  if (error.code === 4001 || errorMessage.includes('User rejected') || errorMessage.includes('denied')) {
    return 'Connection request was rejected. Please try again.';
  }

  // Already pending
  if (error.code === -32002 || errorMessage.includes('already pending')) {
    return 'A connection request is already pending. Please check your wallet.';
  }

  // Network errors
  if (errorMessage.includes('network') || errorMessage.includes('Network')) {
    return 'Network error. Please check your internet connection.';
  }

  // Timeout errors
  if (errorMessage.includes('timeout') || errorMessage.includes('Timeout')) {
    return 'Request timed out. Please try again.';
  }

  return errorMessage;
};