import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const karura = /*#__PURE__*/ defineChain({
    id: 686,
    name: '<PERSON><PERSON><PERSON>',
    network: 'karura',
    nativeCurrency: {
        name: '<PERSON><PERSON><PERSON>',
        symbol: '<PERSON><PERSON>',
        decimals: 18,
    },
    rpcUrls: {
        default: {
            http: ['https://eth-rpc-karura.aca-api.network'],
            webSocket: ['wss://eth-rpc-karura.aca-api.network'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Ka<PERSON>ra Blockscout',
            url: 'https://blockscout.karura.network',
            apiUrl: 'https://blockscout.karura.network/api',
        },
    },
    testnet: false,
});
//# sourceMappingURL=karura.js.map