import { LitElement } from 'lit';
export declare class W3mWalletReceiveView extends LitElement {
    static styles: import("lit").CSSResult;
    private unsubscribe;
    private address;
    private profileName;
    private network;
    private preferredAccountType;
    constructor();
    disconnectedCallback(): void;
    render(): import("lit").TemplateResult<1>;
    networkTemplate(): import("lit").TemplateResult<1> | null;
    onReceiveClick(): void;
    onCopyClick(): void;
}
declare global {
    interface HTMLElementTagNameMap {
        'w3m-wallet-receive-view': W3mWalletReceiveView;
    }
}
