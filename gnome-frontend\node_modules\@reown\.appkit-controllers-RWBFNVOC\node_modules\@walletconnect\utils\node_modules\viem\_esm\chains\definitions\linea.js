import { chainConfig } from '../../linea/chainConfig.js';
import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const linea = /*#__PURE__*/ defineChain({
    ...chainConfig,
    id: 59_144,
    name: 'Linea Mainnet',
    nativeCurrency: { name: 'Linea Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.linea.build'],
            webSocket: ['wss://rpc.linea.build'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Etherscan',
            url: 'https://lineascan.build',
            apiUrl: 'https://api.lineascan.build/api',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 42,
        },
    },
    testnet: false,
});
//# sourceMappingURL=linea.js.map