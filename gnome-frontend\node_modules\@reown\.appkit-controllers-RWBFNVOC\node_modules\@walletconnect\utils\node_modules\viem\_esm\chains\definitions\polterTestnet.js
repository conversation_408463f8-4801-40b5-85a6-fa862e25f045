import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const polterTestnet = /*#__PURE__*/ define<PERSON>hain({
    id: 631571,
    name: 'Polter Testnet',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON> GHST',
        symbol: 'GHST',
    },
    rpcUrls: {
        default: {
            http: ['https://geist-polter.g.alchemy.com/public'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Blockscout',
            url: 'https://polter-testnet.explorer.alchemy.com',
        },
    },
    contracts: {
        multicall3: {
            address: '0xca11bde05977b3631167028862be2a173976ca11',
            blockCreated: 11245,
        },
    },
    testnet: true,
});
//# sourceMappingURL=polterTestnet.js.map