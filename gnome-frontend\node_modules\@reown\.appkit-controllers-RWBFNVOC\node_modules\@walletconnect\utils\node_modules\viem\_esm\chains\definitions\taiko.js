import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const taiko = /*#__PURE__*/ defineChain({
    id: 167000,
    name: 'Taiko Mainnet',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: {
            http: ['https://rpc.mainnet.taiko.xyz'],
            webSocket: ['wss://ws.mainnet.taiko.xyz'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Taikos<PERSON>',
            url: 'https://taikoscan.io',
            apiUrl: 'https://api.taikoscan.io/api',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
        },
    },
});
//# sourceMappingURL=taiko.js.map