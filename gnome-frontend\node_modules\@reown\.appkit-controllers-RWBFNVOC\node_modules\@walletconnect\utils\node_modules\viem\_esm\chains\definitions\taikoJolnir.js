import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js';
export const taikoJolnir = /*#__PURE__*/ define<PERSON>hain({
    id: 167007,
    name: '<PERSON><PERSON> (Alpha-5 Testnet)',
    nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.jolnir.taiko.xyz'],
        },
    },
    blockExplorers: {
        default: {
            name: 'blockscout',
            url: 'https://explorer.jolnir.taiko.xyz',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 732706,
        },
    },
    testnet: true,
});
//# sourceMappingURL=taikoJolnir.js.map