import { useAccount, useSwitch<PERSON>hain, useDisconnect, useWriteContract } from 'wagmi';
import { useState } from 'preact/hooks';
import { requiredChain, requiredChainId } from '../../config/wagmi';
import { useAuth } from '../../context/AuthContext';
import type { ComponentChildren } from 'preact';

interface ChainGuardProps {
  children: ComponentChildren;
}

export function ChainGuard({ children }: ChainGuardProps) {
  const { chain, isConnected } = useAccount();
  const { switchChain, isPending: isSwitching } = useSwitchChain();
  const { disconnect } = useDisconnect();
  const { logout } = useAuth();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Check for any pending transactions that might interfere with chain switching
  const { isPending: isAnyTransactionPending } = useWriteContract();

  // Check if there are any pending transactions that might block chain switching
  const hasPendingTransactions = isAnyTransactionPending ||
    localStorage.getItem('transactionSubmitTime') !== null;

  const handleSwitchChain = async () => {
    // If there are pending transactions, force logout instead of trying to switch
    if (hasPendingTransactions) {
      console.log('Pending transactions detected, forcing logout instead of chain switch');
      handleLogout();
      return;
    }

    try {
      await switchChain({ chainId: requiredChainId });
      // Don't manually hide modal - let useEffect handle it when chain actually changes
    } catch (error) {
      console.error('Failed to switch chain:', error);
      // If switch fails, logout user so they can reconnect with correct chain
      handleLogout();
    }
  };

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      // Logout from auth context
      logout();
      // Disconnect wallet
      disconnect();
      // Redirect to home page
      window.location.href = '/';
    } catch (error) {
      console.error('Failed to logout:', error);
      setIsLoggingOut(false);
    }
  };

  // If user is not connected or on correct chain, show children normally
  if (!isConnected || (isConnected && chain?.id === requiredChainId)) {
    return <>{children}</>;
  }

  // If connected but wrong chain, show modal and block content
  return (
    <div className="relative">
      {/* Blur the background content */}
      <div className="filter blur-sm pointer-events-none">
        {children}
      </div>

      {/* Chain switching modal */}
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-gray-800 rounded-lg p-6 max-w-md mx-4 text-white">
          <div className="text-center">
            <h2 className="text-xl font-bold mb-4">Wrong Network</h2>
            <p className="mb-4">
              You're currently on <span className="font-semibold text-yellow-400">{chain?.name}</span>.
            </p>
            <p className="mb-4">
              Please switch to <span className="font-semibold text-green-400">{requiredChain.name}</span> to continue using the app.
            </p>

            {hasPendingTransactions && (
              <div className="mb-4 p-3 bg-orange-900/50 border border-orange-600 rounded-lg">
                <p className="text-sm text-orange-300">
                  ⚠️ Pending transaction detected. Chain switching will disconnect your wallet.
                </p>
              </div>
            )}

            <div className="space-y-3">
              <button
                onClick={handleSwitchChain}
                disabled={isSwitching || isLoggingOut}
                className="w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-4 py-2 rounded-lg font-medium transition-colors pixelated-text"
              >
                {isSwitching ? 'Switching...' :
                 hasPendingTransactions ? 'Disconnect & Switch' :
                 `Switch to ${requiredChain.name}`}
              </button>

              <button
                onClick={handleLogout}
                disabled={isSwitching || isLoggingOut}
                className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-4 py-2 rounded-lg font-medium transition-colors pixelated-text"
              >
                {isLoggingOut ? 'Logging out...' : 'Disconnect & Go Back'}
              </button>
            </div>

            <div className="mt-4 text-sm text-gray-400">
              <p>Chain ID: {requiredChainId}</p>
              <p>Network: {requiredChain.name}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
