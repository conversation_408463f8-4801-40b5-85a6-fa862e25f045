import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const polygon = /*#__PURE__*/ defineChain({
    id: 137,
    name: 'Polygon',
    nativeCurrency: { name: '<PERSON><PERSON>', symbol: '<PERSON><PERSON>', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://polygon-rpc.com'],
        },
    },
    blockExplorers: {
        default: {
            name: 'PolygonScan',
            url: 'https://polygonscan.com',
            apiUrl: 'https://api.polygonscan.com/api',
        },
    },
    contracts: {
        multicall3: {
            address: '0xca11bde05977b3631167028862be2a173976ca11',
            blockCreated: 25770160,
        },
    },
});
//# sourceMappingURL=polygon.js.map