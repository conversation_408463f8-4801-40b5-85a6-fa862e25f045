import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const sonicTestnet = /*#__PURE__*/ defineChain({
    id: 64_165,
    name: 'Sonic Testnet',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON>',
        symbol: 'S',
    },
    rpcUrls: {
        default: { http: ['https://rpc.testnet.soniclabs.com'] },
    },
    blockExplorers: {
        default: {
            name: 'Sonic Testnet Explorer',
            url: 'https://testnet.soniclabs.com/',
        },
    },
    testnet: true,
});
//# sourceMappingURL=sonicTestnet.js.map