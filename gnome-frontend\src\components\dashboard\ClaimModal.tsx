interface ClaimModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (amount: number | undefined) => void;
  totalCoins: number;
  claimAmount: string;
  onClaimAmountChange: (value: string) => void;
  isLoading: boolean;
}

export function ClaimModal({
  isOpen,
  onClose,
  onConfirm,
  totalCoins,
  claimAmount,
  onClaimAmountChange,
  isLoading
}: ClaimModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
      <div className="bg-[#222222] rounded-lg p-6 shadow-lg max-w-md w-full">
        <h2 className="text-xl pixelated-text mb-4">Claim Tokens</h2>

        <div className="mb-6">
          <p className="pixelated-text text-white mb-2">
            Convert your coins into tokens
          </p>
          <div className="bg-[#191919] p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="pixelated-text text-white">Available Coins:</span>
              <span className="text-green-500 pixelated-text">{totalCoins}</span>
            </div>

            <div className="mt-4">
              <label className="block pixelated-text text-white mb-2">Amount to Claim:</label>
              <input
                type="number"
                value={claimAmount}
                onChange={(e) => {
                  const input = e.target as HTMLInputElement;
                  onClaimAmountChange(input.value);
                }}
                placeholder="Enter amount (leave empty to claim all)"
                className="w-full bg-[#333333] text-white pixelated-text p-2 rounded"
                min="1"
                max={totalCoins}
              />
            </div>
          </div>
        </div>

        <div className="flex justify-between">
          <button 
            className="bg-[#333333] hover:bg-[#444444] px-4 py-2 rounded pixelated-text"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </button>
          <button 
            className="bg-[#432433] hover:bg-[#532d3e] px-4 py-2 rounded pixelated-text"
            onClick={() => onConfirm(claimAmount === '' ? undefined : Number(claimAmount))}
            disabled={isLoading || totalCoins <= 0}
          >
            {isLoading ? 'Processing...' : 'Claim Tokens'}
          </button>
        </div>
      </div>
    </div>
  );
}
