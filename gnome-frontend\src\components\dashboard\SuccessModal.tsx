interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  message: string;
}

export function SuccessModal({
  isOpen,
  onClose,
  message
}: SuccessModalProps) {
  if (!isOpen) return null;

  return (
    <div 
      className="fixed top-6 left-1/2 z-50 animate-fade-in-down"
      style={{ 
        maxWidth: '90vw',
        transform: 'translateX(-50%)',
        willChange: 'transform'
      }}
    >
      <div 
        className="bg-green-800/90 backdrop-blur-sm rounded-lg px-6 py-3 shadow-lg border border-green-600/50 flex items-center gap-3"
        onClick={onClose}
      >
        <div className="text-green-400 text-xl">✓</div>
        <p className="text-white pixelated-text">{message}</p>
      </div>
    </div>
  );
}
