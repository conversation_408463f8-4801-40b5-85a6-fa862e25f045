import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const mchVerse = /*#__PURE__*/ define<PERSON>hain({
    id: 29548,
    name: 'MCH Verse',
    nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: 'OAS', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.oasys.mycryptoheroes.net'],
        },
    },
    blockExplorers: {
        default: {
            name: 'MCH Verse Explorer',
            url: 'https://explorer.oasys.mycryptoheroes.net',
            apiUrl: 'https://explorer.oasys.mycryptoheroes.net/api',
        },
    },
    testnet: false,
});
//# sourceMappingURL=mchVerse.js.map