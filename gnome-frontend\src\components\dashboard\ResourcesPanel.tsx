import copperImage from '../../assets/Copper.png';
import silverImage from '../../assets/Silver.png';
import goldImage from '../../assets/Gold.png';
import telegramIcon from '../../assets/TG-icon.png';
import xIcon from '../../assets/x-icon.png';
import { useState } from 'preact/hooks';
import { HowToPlayModal } from './HowToPlayModal';

interface ResourcesPanelProps {
  stacks: {
    totalCopper: number;
    totalSilver: number;
    totalGold: number;
  };
  globalStats: {
    totalOres: {
      total: number;
      copper: number;
      silver: number;
      gold: number;
    };
    totalCoins: number;
    totalClaimedCoins: number;
  };
}

export function ResourcesPanel({
  stacks,
  globalStats
}: ResourcesPanelProps) {
  const [showHowToPlay, setShowHowToPlay] = useState(false);
  return (
    <div className="bg-[#191919] rounded-lg p-6 shadow-lg md:col-span-1">
      <h2 className="text-xl pixelated-text mb-6">YOUR RESOURCES</h2>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <img src={copperImage} alt="Copper" className="w-5 h-5" />
            <span className="pixelated-text text-white">Copper</span>
          </div>
          <span className="text-green-500 pixelated-text">{stacks.totalCopper}</span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <img src={silverImage} alt="Silver" className="w-5 h-5" />
            <span className="pixelated-text text-white">Silver</span>
          </div>
          <span className="text-green-500 pixelated-text">{stacks.totalSilver}</span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <img src={goldImage} alt="Gold" className="w-5 h-5" />
            <span className="pixelated-text text-white">Gold</span>
          </div>
          <span className="text-green-500 pixelated-text">{stacks.totalGold}</span>
        </div>
      </div>

      <div className="mt-8">
        <h2 className="text-xl pixelated-text mb-4">GLOBAL STATS</h2>
        <div className="bg-[#222222] p-4 rounded-lg">
          <p className="pixelated-text text-white/80 leading-relaxed mb-3">
            A total of <span className="text-yellow-500">{globalStats.totalOres.total.toLocaleString()}</span> ores have been claimed by miners across the realm, including <span className="text-yellow-500">{globalStats.totalOres.copper.toLocaleString()}</span> copper, <span className="text-yellow-500">{globalStats.totalOres.silver.toLocaleString()}</span> silver, and <span className="text-yellow-500">{globalStats.totalOres.gold.toLocaleString()}</span> gold.
          </p>
          <p className="pixelated-text text-white/80 leading-relaxed">
            The treasury has distributed <span className="text-green-500 font-bold">{globalStats.totalClaimedCoins.toLocaleString()}</span> $CAVE to adventurers throughout the cave.
          </p>
        </div>
      </div>

      <div className="mt-6 space-y-3">
        {/* Social Links */}
        <div className="flex justify-center gap-4">
          <a 
            href="https://t.me/thecavegame" 
            target="_blank" 
            rel="noopener noreferrer"
            className="bg-[#222222] hover:bg-[#2c2c2c] p-2 rounded transition-colors"
          >
            <img src={telegramIcon} alt="Telegram" className="w-5 h-5" />
            <span className="sr-only">Telegram</span>
          </a>
          <a 
            href="https://x.com/thecavegame" 
            target="_blank" 
            rel="noopener noreferrer"
            className="bg-[#222222] hover:bg-[#2c2c2c] p-2 rounded transition-colors"
          >
            <img src={xIcon} alt="X (Twitter)" className="w-5 h-5" />
            <span className="sr-only">X (Twitter)</span>
          </a>
        </div>

        {/* How to Play Button */}
        <button 
          onClick={() => setShowHowToPlay(true)}
          className="w-full bg-[#432433] hover:bg-[#532d3e] text-white pixelated-text py-2 px-4 rounded transition-colors flex items-center justify-center gap-2"
        >
          <span className="text-xl">📖</span>
          How to Play
        </button>

        <HowToPlayModal 
          isOpen={showHowToPlay}
          onClose={() => setShowHowToPlay(false)}
        />
      </div>
    </div>
  );
}
