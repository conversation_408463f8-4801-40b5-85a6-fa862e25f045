{"version": 3, "file": "formatters.js", "sourceRoot": "", "sources": ["../../celo/formatters.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAA;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAA;AAC1D,OAAO,EACL,iBAAiB,EACjB,iBAAiB,GAClB,MAAM,oCAAoC,CAAA;AAC3C,OAAO,EAAE,wBAAwB,EAAE,MAAM,2CAA2C,CAAA;AASpF,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAA;AAEpC,MAAM,CAAC,MAAM,UAAU,GAAG;IACxB,KAAK,EAAE,aAAa,CAAC,WAAW,CAAC;QAC/B,MAAM,CAAC,IAAkB;YACvB,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;gBAC1D,IAAI,OAAO,WAAW,KAAK,QAAQ;oBAAE,OAAO,WAAW,CAAA;gBACvD,MAAM,SAAS,GAAG,iBAAiB,CAAC,WAA6B,CAAC,CAAA;gBAClE,OAAO;oBACL,GAAG,SAAS;oBACZ,GAAG,CAAC,WAAW,CAAC,UAAU;wBACxB,CAAC,CAAC;4BACE,UAAU,EAAE,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC;4BAC/C,mBAAmB,EAAE,WAAW,CAAC,mBAAmB;yBACrD;wBACH,CAAC,CAAC,EAAE,CAAC;oBACP,WAAW,EAAE,WAAW,CAAC,WAAW;iBACrC,CAAA;YACH,CAAC,CAAC,CAAA;YACF,OAAO;gBACL,YAAY;gBACZ,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;aAC/C,CAAA;QAChB,CAAC;KACF,CAAC;IACF,WAAW,EAAE,aAAa,CAAC,iBAAiB,CAAC;QAC3C,MAAM,CAAC,IAAwB;YAC7B,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;gBACtB,OAAO;oBACL,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;oBACpD,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,IAAI,EAAE,SAAS;iBACG,CAAA;YAEtB,MAAM,WAAW,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAqB,CAAA;YAExE,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;gBAAE,WAAW,CAAC,IAAI,GAAG,OAAO,CAAA;iBAC/C,CAAC;gBACJ,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;oBAAE,WAAW,CAAC,IAAI,GAAG,OAAO,CAAA;gBAEpD,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;oBACtC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;oBAC9B,CAAC,CAAC,IAAI,CAAA;gBACR,WAAW,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAA;YAC5D,CAAC;YAED,OAAO,WAAW,CAAA;QACpB,CAAC;KACF,CAAC;IACF,kBAAkB,EAAE,aAAa,CAAC,wBAAwB,CAAC;QACzD,MAAM,CAAC,IAA4B;YACjC,MAAM,OAAO,GAAG,EAA+B,CAAA;YAE/C,IAAI,IAAI,CAAC,WAAW;gBAAE,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;YAC5D,IAAI,OAAO,CAAC,IAAI,CAAC;gBAAE,OAAO,CAAC,IAAI,GAAG,MAAM,CAAA;YAExC,OAAO,OAAO,CAAA;QAChB,CAAC;KACF,CAAC;CACgC,CAAA"}