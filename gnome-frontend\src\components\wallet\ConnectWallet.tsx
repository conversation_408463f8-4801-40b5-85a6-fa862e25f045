import { useConnect, useAccount, useDisconnect } from 'wagmi';
import { useEffect } from 'preact/hooks';
import { useAuth } from '../../context/AuthContext';
import { isMobile } from '../../utils/wallet-utils';

export function ConnectWallet() {
  const { connect, connectors, isPending } = useConnect();
  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();

  // Auto-login when wallet connects
  useEffect(() => {
    if (isConnected && address && !isAuthenticated && !authLoading) {
      console.log('Wallet connected, attempting auto-login...');
      login();
    }
  }, [isConnected, address, isAuthenticated, authLoading, login]);

  // Clean wallet connection for production
  const handleConnect = async () => {
    try {
      // If already connected, just authenticate
      if (isConnected) {
        await login();
        return;
      }

      // For mobile devices, try direct provider first
      if (isMobile() && window.ethereum) {
        try {
          await window.ethereum.request({ method: 'eth_requestAccounts' });
          // Wait for connection to establish
          await new Promise(resolve => setTimeout(resolve, 500));
          return;
        } catch (directError) {
          console.log('Direct provider failed, using wagmi connectors');
        }
      }

      // Use appropriate connector
      const connector = isMobile()
        ? connectors.find(c => c.id === 'injected') || connectors[0]
        : connectors.find(c => c.name === 'MetaMask') || connectors[0];

      if (!connector) {
        console.error('No wallet connectors available');
        return;
      }

      connect({ connector });

    } catch (error: any) {
      console.error('Wallet connection failed:', error);

      // Don't show error for user rejection
      if (error.code !== 4001) {
        console.error('Connection error:', error.message);
      }
    }
  };

  // Handle wallet disconnection
  const handleDisconnect = () => {
    disconnect();
  };

  return (
    <button
      className="connect-wallet-btn rounded pixelated-text"
      onClick={isAuthenticated ? handleDisconnect : handleConnect}
      disabled={isPending || authLoading}
    >
      {isPending
        ? 'Connecting...'
        : authLoading
        ? 'Authenticating...'
        : isAuthenticated
        ? 'Disconnect'
        : isConnected
        ? 'Authenticate'
        : 'Connect Wallet'}
    </button>
  );
}
