import api from './api';

// Stacks API endpoints
export const stacksApi = {
  // Get stacks information
  getStacks: async () => {
    const response = await api.get('/stacks');
    return response.data;
  },

  // Smelt a specific stack
  smeltStack: async (type: string) => {
    const response = await api.post(`/stacks/smelt/${type}`);
    return response.data;
  },

  // Smelt all stacks
  smeltAllStacks: async () => {
    const response = await api.post('/stacks/smelt-all');
    return response.data;
  }
};

export default stacksApi;
