import { http, createConfig } from 'wagmi';
import { mainnet, baseSepolia, base } from 'wagmi/chains';
import { injected, metaMask, coinbaseWallet } from 'wagmi/connectors';

// Get required chain ID from environment variable
const requiredChainId = parseInt(import.meta.env.VITE_REQUIRED_CHAIN_ID || '84532');

// Configure supported chains - ensure at least one chain is always present
const supportedChains = [baseSepolia, base, mainnet] as const;

// Get the required chain
const requiredChain = supportedChains.find(chain => chain.id === requiredChainId) || baseSepolia;

// FIXED: Simple connector configuration that actually works
const connectors = [
  // MetaMask connector - most common
  metaMask(),
  // Generic injected connector for other wallets
  injected(),
  // Coinbase Wallet
  coinbaseWallet({
    appName: 'The Cave',
  }),
];

// Configure chains and providers
export const config = createConfig({
  chains: supportedChains,
  transports: {
    [baseSepolia.id]: http(),
    [base.id]: http(),
    [mainnet.id]: http(),
  },
  connectors,
});

// Export required chain info for use in components
export { requiredChain, requiredChainId };
