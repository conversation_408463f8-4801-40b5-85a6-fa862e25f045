import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const kcc = /*#__PURE__*/ defineChain({
    id: 321,
    name: 'KCC Mainnet',
    network: 'KCC Mainnet',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: 'KC<PERSON>',
    },
    rpcUrls: {
        default: {
            http: ['https://kcc-rpc.com'],
        },
    },
    blockExplorers: {
        default: { name: 'KCC Explorer', url: 'https://explorer.kcc.io' },
    },
    contracts: {
        multicall3: {
            address: '0xca11bde05977b3631167028862be2a173976ca11',
            blockCreated: 11760430,
        },
    },
    testnet: false,
});
//# sourceMappingURL=kcc.js.map