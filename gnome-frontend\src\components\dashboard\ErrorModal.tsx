interface ErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  message: string;
  retryAction?: () => void;
}

export function ErrorModal({
  isOpen,
  onClose,
  message,
  retryAction
}: ErrorModalProps) {
  if (!isOpen) return null;

  return (
    <div 
      className="fixed top-6 left-1/2 z-50 animate-fade-in-down"
      style={{ 
        maxWidth: '90vw',
        transform: 'translateX(-50%)',
        willChange: 'transform'
      }}
    >
      <div 
        className="bg-red-800/90 backdrop-blur-sm rounded-lg px-6 py-3 shadow-lg border border-red-600/50 flex items-center gap-3"
      >
        <div className="text-red-400 text-xl">⚠</div>
        <div className="flex-1">
          <p className="text-white pixelated-text">{message}</p>
          {retryAction && (
            <div className="mt-2 flex justify-end">
              <button 
                className="bg-red-700 hover:bg-red-600 px-3 py-1 rounded text-sm pixelated-text text-white"
                onClick={retryAction}
              >
                Retry
              </button>
            </div>
          )}
        </div>
        <button 
          className="text-red-400 hover:text-red-300 text-xl"
          onClick={onClose}
        >
          ×
        </button>
      </div>
    </div>
  );
}
