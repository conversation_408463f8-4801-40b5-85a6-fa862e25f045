import { useState, useEffect } from 'preact/hooks';
import { useAccount, useWriteContract, useWaitForTransactionReceipt } from 'wagmi';
import storeApi from '../services/store';
import { paymentGatewayAbi } from '../services/payment-gateway.abi';

// Get the contract address from environment variable
const PAYMENT_GATEWAY_ADDRESS = import.meta.env.VITE_PAYMENT_GATEWAY_ADDRESS;

// ERC20 token ABI for approval
const erc20Abi = [
  // approve function
  {
    "inputs": [
      {
        "internalType": "address",
        "name": "spender",
        "type": "address"
      },
      {
        "internalType": "uint256",
        "name": "amount",
        "type": "uint256"
      }
    ],
    "name": "approve",
    "outputs": [
      {
        "internalType": "bool",
        "name": "",
        "type": "bool"
      }
    ],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

export function useUpgrade() {
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [upgradeError, setUpgradeError] = useState<string | null>(null);
  const [upgradeSuccess, setUpgradeSuccess] = useState(false);
  const { address } = useAccount();

  // Contract write hook
  const { writeContract, isPending, data: writeData } = useWriteContract();

  // Transaction receipt hook
  const { isLoading: isTransactionLoading, error: transactionError, isSuccess: isTransactionSuccess } = useWaitForTransactionReceipt({
    hash: writeData,
  });

  // Handle transaction success and error
  useEffect(() => {
    if (writeData) {
      if (isTransactionSuccess) {
        // Transaction completed successfully
        console.log('Transaction confirmed successfully');
        setUpgradeError(null); // Clear any waiting messages
        setUpgradeSuccess(true);
        setIsUpgrading(false);
      } else if (!isTransactionLoading && !transactionError) {
        // Transaction is no longer loading but not yet marked as success
        // This could be a transitional state, so we'll log it
        console.log('Transaction no longer loading but not yet marked as success');

        // Check if we should force success based on elapsed time
        const transactionTime = localStorage.getItem('transactionSubmitTime');
        if (transactionTime && upgradeError === 'Upgrade transaction submitted. Waiting for blockchain confirmation...') {
          const currentTime = new Date().getTime();
          const elapsedTime = currentTime - parseInt(transactionTime);
          // If it's been more than 5 seconds since submission and transaction is no longer loading,
          // assume it's successful even if not explicitly marked as success
          if (elapsedTime > 5000) {
            console.log('Forcing transaction success based on elapsed time in success handler');
            setUpgradeError(null);
            setUpgradeSuccess(true);
            setIsUpgrading(false);
          }
        }
      }
    }
  }, [writeData, isTransactionSuccess, isTransactionLoading, transactionError, upgradeError]);

  // Check transaction status periodically
  useEffect(() => {
    // Function to check transaction status
    const checkTransactionStatus = () => {
      const transactionTime = localStorage.getItem('transactionSubmitTime');
      if (transactionTime && upgradeError === 'Upgrade transaction submitted. Waiting for blockchain confirmation...') {
        const currentTime = new Date().getTime();
        const elapsedTime = currentTime - parseInt(transactionTime);
        
        // If it's been more than 30 seconds, assume transaction is complete
        if (elapsedTime > 30000) {
          console.log('Forcing transaction success based on elapsed time in polling');
          setUpgradeError(null);
          setUpgradeSuccess(true);
          setIsUpgrading(false);
          localStorage.removeItem('transactionSubmitTime');
        }
      }
    };

    // Check immediately
    checkTransactionStatus();
    
    // Then set up interval to check every 5 seconds
    const interval = setInterval(checkTransactionStatus, 5000);
    
    // Clean up interval on unmount
    return () => clearInterval(interval);
  }, [upgradeError]);

  // Reset state
  const resetState = () => {
    setUpgradeError(null);
    setUpgradeSuccess(false);
    setIsUpgrading(false);
  };

  // Upgrade bag
  const upgradeBag = async (miningId: string, nextBagId: number) => {
    if (!address) {
      setUpgradeError('Wallet not connected');
      return;
    }

    try {
      resetState();
      setIsUpgrading(true);

      // STEP 1: Call the API to get transaction details
      setUpgradeError('Preparing transaction...');
      const transactionDetails = await storeApi.upgradeBag(miningId, nextBagId);
      console.log('Upgrade bag transaction details:', transactionDetails);

      // Extract transaction parameters
      const { arguments: args, signature } = transactionDetails;
      const { orderId, amount, token, merchant } = args;

      // Format parameters
      const formattedToken = token.toLowerCase() as `0x${string}`;
      const formattedMerchant = merchant.toLowerCase() as `0x${string}`;
      const formattedSignature = signature.startsWith('0x') ? signature : `0x${signature}`;
      const amountBigInt = BigInt(amount);

      // Store transaction submission time for tracking
      localStorage.setItem('transactionSubmitTime', new Date().getTime().toString());

      // STEP 2: First approve the token spending
      setUpgradeError('Please approve token spending in your wallet...');

      // This will show the wallet popup for token approval
      await writeContract({
        address: formattedToken,
        abi: erc20Abi,
        functionName: 'approve',
        args: [
          PAYMENT_GATEWAY_ADDRESS,
          amountBigInt
        ]
      });

      // Wait a bit for the approval to be processed
      setUpgradeError('Token approval confirmed. Preparing upgrade transaction...');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // STEP 3: Now call the payWithToken function
      setUpgradeError('Please confirm the upgrade transaction in your wallet...');

      // This will show the wallet popup for the purchase
      await writeContract({
        address: PAYMENT_GATEWAY_ADDRESS,
        abi: paymentGatewayAbi,
        functionName: 'payWithToken',
        args: [
          orderId,
          amountBigInt,
          formattedToken,
          formattedMerchant,
          formattedSignature
        ]
      });

      // Transaction has been confirmed by the user in wallet but not yet by the blockchain
      setUpgradeError('Upgrade transaction submitted. Waiting for blockchain confirmation...');
      
      // The success state will be set by the useEffect hooks when the transaction is confirmed
    } catch (error: any) {
      console.error('Upgrade bag error:', error);
      
      // Determine the appropriate error message
      let errorMessage = 'Failed to upgrade bag';

      if (error.code === 4001) {
        // User rejected the transaction in wallet
        errorMessage = 'Transaction was rejected in your wallet';
      } else if (error.response?.data?.message) {
        // Backend API error
        errorMessage = error.response.data.message;
      } else if (error.message) {
        // General error with message
        errorMessage = error.message;
      }
      
      setUpgradeError(errorMessage);
      setIsUpgrading(false);
    }
  };

  // Upgrade pickaxe
  const upgradePickaxe = async (miningId: string, nextPickaxeId: number) => {
    if (!address) {
      setUpgradeError('Wallet not connected');
      return;
    }

    try {
      resetState();
      setIsUpgrading(true);

      // STEP 1: Call the API to get transaction details
      setUpgradeError('Preparing transaction...');
      const transactionDetails = await storeApi.upgradePickaxe(miningId, nextPickaxeId);
      console.log('Upgrade pickaxe transaction details:', transactionDetails);

      // Extract transaction parameters
      const { arguments: args, signature } = transactionDetails;
      const { orderId, amount, token, merchant } = args;

      // Format parameters
      const formattedToken = token.toLowerCase() as `0x${string}`;
      const formattedMerchant = merchant.toLowerCase() as `0x${string}`;
      const formattedSignature = signature.startsWith('0x') ? signature : `0x${signature}`;
      const amountBigInt = BigInt(amount);

      // Store transaction submission time for tracking
      localStorage.setItem('transactionSubmitTime', new Date().getTime().toString());

      // STEP 2: First approve the token spending
      setUpgradeError('Please approve token spending in your wallet...');

      // This will show the wallet popup for token approval
      await writeContract({
        address: formattedToken,
        abi: erc20Abi,
        functionName: 'approve',
        args: [
          PAYMENT_GATEWAY_ADDRESS,
          amountBigInt
        ]
      });

      // Wait a bit for the approval to be processed
      setUpgradeError('Token approval confirmed. Preparing upgrade transaction...');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // STEP 3: Now call the payWithToken function
      setUpgradeError('Please confirm the upgrade transaction in your wallet...');

      // This will show the wallet popup for the purchase
      await writeContract({
        address: PAYMENT_GATEWAY_ADDRESS,
        abi: paymentGatewayAbi,
        functionName: 'payWithToken',
        args: [
          orderId,
          amountBigInt,
          formattedToken,
          formattedMerchant,
          formattedSignature
        ]
      });

      // Transaction has been confirmed by the user in wallet but not yet by the blockchain
      setUpgradeError('Upgrade transaction submitted. Waiting for blockchain confirmation...');
      
      // The success state will be set by the useEffect hooks when the transaction is confirmed
    } catch (error: any) {
      console.error('Upgrade pickaxe error:', error);
      
      // Determine the appropriate error message
      let errorMessage = 'Failed to upgrade pickaxe';

      if (error.code === 4001) {
        // User rejected the transaction in wallet
        errorMessage = 'Transaction was rejected in your wallet';
      } else if (error.response?.data?.message) {
        // Backend API error
        errorMessage = error.response.data.message;
      } else if (error.message) {
        // General error with message
        errorMessage = error.message;
      }
      
      setUpgradeError(errorMessage);
      setIsUpgrading(false);
    }
  };

  return {
    upgradeBag,
    upgradePickaxe,
    isUpgrading: isUpgrading || isPending || isTransactionLoading,
    upgradeError,
    upgradeSuccess,
    resetUpgradeState: resetState
  };
}

export default useUpgrade;
