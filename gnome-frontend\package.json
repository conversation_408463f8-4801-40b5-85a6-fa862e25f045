{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.5", "@tanstack/react-query": "^5.75.7", "@types/react-slick": "^0.23.13", "axios": "^1.9.0", "preact": "^10.26.5", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "viem": "^2.29.2", "wagmi": "^2.15.2"}, "devDependencies": {"@preact/preset-vite": "^2.10.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "typescript": "~5.8.3", "vite": "^6.3.5"}}