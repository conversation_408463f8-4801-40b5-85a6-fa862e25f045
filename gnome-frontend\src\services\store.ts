import api from './api';

// Store API endpoints
export const storeApi = {
  // Get all available miners
  getAllMiners: async () => {
    const response = await api.get('/store/miners');
    return response.data;
  },

  // Buy a miner
  buyMiner: async (minerItemId: number) => {
    const response = await api.post('/store/buy-miner', {
      minerItemId
    });
    return response.data;
  },

  // Upgrade a miner's bag
  upgradeBag: async (miningId: string, bagItemId: number) => {
    const response = await api.post('/store/upgrade-bag', {
      miningId,
      bagItemId
    });
    return response.data;
  },

  // Upgrade a miner's pickaxe
  upgradePickaxe: async (miningId: string, pickaxeItemId: number) => {
    const response = await api.post('/store/upgrade-pickaxe', {
      miningId,
      pickaxeItemId
    });
    return response.data;
  },
  
  // Get available bag upgrades
  getAvailableBags: async () => {
    const response = await api.get('/store/bags');
    return response.data;
  },
  
  // Get available pickaxe upgrades
  getAvailablePickaxes: async () => {
    const response = await api.get('/store/pickaxes');
    return response.data;
  }
};

export default storeApi;
