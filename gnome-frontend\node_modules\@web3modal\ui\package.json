{"name": "@web3modal/ui", "version": "5.1.11", "type": "module", "main": "./dist/esm/index.js", "types": "./dist/types/index.d.ts", "files": ["dist", "!tsconfig.tsbuildinfo"], "dependencies": {"lit": "3.1.0", "qrcode": "1.5.3"}, "devDependencies": {"@types/qrcode": "1.5.5", "eslint-plugin-lit": "1.11.0", "eslint-plugin-wc": "2.0.4", "vitest": "2.0.3", "@vitest/coverage-v8": "2.0.5", "@web3modal/common": "5.1.11", "@web3modal/core": "5.1.11", "@web3modal/wallet": "5.1.11"}, "keywords": ["web3", "crypto", "ethereum", "web3modal", "walletconnect", "lit", "webcomponents"], "author": "WalletConnect <walletconnect.com>", "license": "Apache-2.0", "homepage": "https://github.com/web3modal/web3modal", "repository": {"type": "git", "url": "git+https://github.com/web3modal/web3modal.git"}, "bugs": {"url": "https://github.com/web3modal/web3modal/issues"}, "scripts": {"build:clean": "rm -rf dist", "build": "tsc --build", "watch": "tsc --watch", "test": "vitest run --dir tests --coverage.enabled --reporter=junit --coverage.reporter=json-summary --coverage.reporter=html", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}}