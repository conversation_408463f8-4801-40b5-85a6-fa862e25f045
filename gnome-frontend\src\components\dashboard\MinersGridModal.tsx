import { useEffect, useRef, useState } from 'preact/hooks';
import { getMinerImage, getMinerProfilePicture } from '../../utils/miner-utils';
import { getBagDetails, getPickaxeDetails } from '../../utils/item-utils';
import copperImage from '../../assets/Copper.png';
import silverImage from '../../assets/Silver.png';
import goldImage from '../../assets/Gold.png';

interface MinersGridModalProps {
  isOpen: boolean;
  onClose: () => void;
  miners: any[];
  miningActionLoading: {[key: string]: boolean};
  onStartMining: (miningId: string) => void;
  onStopMining: (miningId: string) => void;
  onStackMining: (miningId: string) => void;
  isMiningInProgress: (mining: any) => boolean;
  getMiningStatusText: (mining: any) => string;
  countdowns: {[key: string]: string};
}

export function MinersGridModal({
  isOpen,
  onClose,
  miners,
  miningActionLoading,
  onStartMining,
  onStopMining,
  onStackMining,
  isMiningInProgress,
  getMiningStatusText,
  countdowns
}: MinersGridModalProps) {
  // Use ref to trap focus inside modal
  const modalRef = useRef<HTMLDivElement>(null);
  
  // State for filtering miners
  const [filterType, setFilterType] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('default');

  // Handle escape key to close modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  // Handle click outside to close
  const handleBackdropClick = (e: MouseEvent) => {
    if (modalRef.current && e.target === modalRef.current) {
      onClose();
    }
  };
  
  // Filter and sort miners
  const filteredMiners = miners.filter(mining => {
    // Filter by miner type
    if (filterType !== 'all' && mining.miner.name.toUpperCase() !== filterType.toUpperCase()) {
      return false;
    }
    
    return true;
  }).sort((a, b) => {
    // Sort miners
    switch (sortBy) {
      case 'name':
        return a.miner.name.localeCompare(b.miner.name);
      case 'status':
        return a.mining.status.localeCompare(b.mining.status);
      case 'speed':
        return b.miner.miningSpeed - a.miner.miningSpeed;
      default:
        return 0;
    }
  });
  
  // Get unique miner types
  const minerTypes = [...new Set(miners.map(mining => mining.miner.name))];

  if (!isOpen) return null;

  return (
    <div 
      ref={modalRef}
      className="fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center p-4 overflow-y-auto backdrop-blur-sm"
      onClick={handleBackdropClick}
    >
      <div className="bg-gradient-to-b from-[#191919] to-[#141414] rounded-lg border border-[#333] shadow-2xl p-6 max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4 border-b border-[#333] pb-4">
          <h2 className="text-2xl font-bold text-yellow-400">All Miners</h2>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white bg-[#333] hover:bg-[#444] rounded-full p-1 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-6 bg-[#222] p-4 rounded-lg border border-[#333]">
          <div>
            <label className="block text-sm text-gray-400 mb-1">Filter by Type</label>
            <select 
              className="bg-[#333] border border-[#444] rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
              value={filterType}
              onChange={(e) => setFilterType((e.target as HTMLSelectElement).value)}
            >
              <option value="all">All Types</option>
              {minerTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm text-gray-400 mb-1">Sort By</label>
            <select 
              className="bg-[#333] border border-[#444] rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
              value={sortBy}
              onChange={(e) => setSortBy((e.target as HTMLSelectElement).value)}
            >
              <option value="default">Default</option>
              <option value="name">Name</option>
              <option value="status">Status</option>
              <option value="speed">Mining Speed</option>
            </select>
          </div>
        </div>
        
        {/* Stats Summary */}
        <div className="mb-6 bg-[#222] p-4 rounded-lg border border-[#333] flex flex-wrap gap-4 justify-around">
          <div className="text-center">
            <div className="text-sm text-gray-400">Total Miners</div>
            <div className="text-xl text-white">{filteredMiners.length}</div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-400">Mining</div>
            <div className="text-xl text-green-500">{filteredMiners.filter(m => isMiningInProgress(m)).length}</div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-400">Ready to Harvest</div>
            <div className="text-xl text-yellow-500">{filteredMiners.filter(m => m.mining.status === 'READY_TO_SMELT').length}</div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-400">Idle</div>
            <div className="text-xl text-gray-500">{filteredMiners.filter(m => m.mining.status === 'IDLE').length}</div>
          </div>
        </div>

        {filteredMiners.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredMiners.map(mining => (
            <div 
              key={mining.id} 
              className="bg-[#222222] rounded-lg p-4 shadow-lg outline-none focus:outline-none"
            >
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center gap-2">
                  <img 
                    src={getMinerProfilePicture(mining.miner.name)} 
                    alt={mining.miner.name} 
                    className="w-8 h-8 object-contain" 
                  />
                  <span className="font-bold">{mining.miner.name}</span>
                </div>
                <span className={`text-xs px-2 py-1 rounded-full text-black ${mining.mining.status === 'READY_TO_SMELT' ? 'bg-yellow-500' : isMiningInProgress(mining) ? 'bg-green-500' : 'bg-gray-500'}`}>
                  {getMiningStatusText(mining)}
                </span>
              </div>

              <div className="flex flex-col gap-1 mb-3 text-xs">
                {/* Bag info */}
                <div className="flex justify-between items-center">
                  <span>Bag:</span>
                  <span className="text-yellow-400">{getBagDetails(mining.bag.id).name} ({mining.bag.slots} slots)</span>
                </div>
                
                {/* Pickaxe info */}
                <div className="flex justify-between items-center">
                  <span>Pickaxe:</span>
                  <span className="text-yellow-400">{getPickaxeDetails(mining.pickaxe.id).name}</span>
                </div>
                
                {/* Mining speed */}
                <div className="flex justify-between items-center">
                  <span>Mining Speed:</span>
                  <span className="text-green-400">{mining.miner.miningSpeed}x</span>
                </div>
                
                {/* Ores */}
                <div className="flex justify-between items-center">
                  <span>Ores:</span>
                  <span className="text-green-400">
                    +{mining.miner.copperRate} Cu +{mining.miner.silverRate} Ag +{mining.miner.goldRate} Au
                  </span>
                </div>
                
                {/* Bonuses */}
                <div className="flex justify-between items-center">
                  <span>Bonuses:</span>
                  <span className="text-green-400">
                    {mining.miner.bonuses ? mining.miner.bonuses : '-'}
                  </span>
                </div>
                
                {/* Time remaining */}
                <div className="flex justify-between items-center">
                  <span>Time remaining:</span>
                  <span className="text-green-400">{countdowns[mining.id] || '-'}</span>
                </div>
              </div>

              {/* Miner image */}
              <div className="flex justify-center items-center h-32 mb-4 bg-[#1a1a1a] rounded-lg overflow-hidden">
                <img 
                  src={getMinerImage(mining.miner.name, isMiningInProgress(mining))} 
                  alt={`${mining.miner.name} ${isMiningInProgress(mining) ? 'mining' : 'idle'}`}
                  className="h-full object-contain" 
                />
              </div>

              {/* Ores collection */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  {/* Copper */}
                  <div className="flex flex-col items-center">
                    <img src={copperImage} alt="Copper" className="w-8 h-8 mb-1" />
                    <span className="text-xs">{mining.mining.copper}</span>
                  </div>
                  
                  {/* Silver */}
                  <div className="flex flex-col items-center">
                    <img src={silverImage} alt="Silver" className="w-8 h-8 mb-1" />
                    <span className="text-xs">{mining.mining.silver}</span>
                  </div>
                  
                  {/* Gold */}
                  <div className="flex flex-col items-center">
                    <img src={goldImage} alt="Gold" className="w-8 h-8 mb-1" />
                    <span className="text-xs">{mining.mining.gold}</span>
                  </div>
                </div>
                
                {/* Bag capacity indicator */}
                <div className="mt-2 bg-[#333333] h-2 rounded-full overflow-hidden">
                  <div 
                    className="bg-green-500 h-full" 
                    style={{ width: `${((mining.mining.copper + mining.mining.silver + mining.mining.gold) / mining.bag.slots) * 100}%` }}
                  ></div>
                </div>
                <div className="text-xs text-center mt-1 text-gray-400">
                  {mining.mining.copper + mining.mining.silver + mining.mining.gold} / {mining.bag.slots} slots
                </div>
              </div>

              <div className="flex justify-between">
                <button
                  className={`px-3 py-1 rounded text-sm ${isMiningInProgress(mining) ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'}`}
                  onClick={() => isMiningInProgress(mining) ? onStopMining(mining.id) : onStartMining(mining.id)}
                  disabled={miningActionLoading[mining.id] || false}
                >
                  {miningActionLoading[mining.id] ? 'Processing...' : isMiningInProgress(mining) ? 'Stop Mining' : 'Start Mining'}
                </button>

                <button
                  className="bg-yellow-600 hover:bg-yellow-700 px-3 py-1 rounded text-sm"
                  onClick={() => onStackMining(mining.id)}
                  disabled={(mining.mining.copper + mining.mining.silver + mining.mining.gold) === 0 || mining.mining.status !== 'READY_TO_SMELT' || miningActionLoading[mining.id]}
                >
                  {miningActionLoading[mining.id] ? 'Processing...' : 'Harvest'}
                </button>
              </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-[#222] p-8 rounded-lg border border-[#333] text-center">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl text-yellow-400 mb-2">No Miners Found</h3>
            <p className="text-gray-400">
              {filterType !== 'all' ? 
                `No ${filterType} miners found` : 
                'No miners available'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
