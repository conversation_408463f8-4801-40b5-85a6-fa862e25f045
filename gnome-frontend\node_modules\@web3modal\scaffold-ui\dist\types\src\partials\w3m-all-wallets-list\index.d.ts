import { LitElement } from 'lit';
export declare class W3mAllWalletsList extends LitElement {
    static styles: import("lit").CSSResult;
    private unsubscribe;
    private paginationObserver?;
    private initial;
    private wallets;
    private recommended;
    private featured;
    constructor();
    firstUpdated(): void;
    disconnectedCallback(): void;
    render(): import("lit").TemplateResult<1>;
    private initialFetch;
    private shimmerTemplate;
    private walletsTemplate;
    private paginationLoaderTemplate;
    private createPaginationObserver;
    private onConnectWallet;
}
declare global {
    interface HTMLElementTagNameMap {
        'w3m-all-wallets-list': W3mAllWalletsList;
    }
}
