import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js';
export const lightlinkPegasus = /*#__PURE__*/ define<PERSON>hain({
    id: 1_891,
    name: 'LightLink Pegasus Testnet',
    network: 'lightlink-pegasus',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: {
            http: ['https://replicator.pegasus.lightlink.io/rpc/v1'],
        },
    },
    blockExplorers: {
        default: {
            name: 'LightLink Pegasus Explorer',
            url: 'https://pegasus.lightlink.io',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 127_188_532,
        },
    },
    testnet: true,
});
//# sourceMappingURL=lightlinkPegasus.js.map