import { chainConfig } from '../../op-stack/chainConfig.js';
import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
const sourceId = 1; // mainnet
export const unichain = /*#__PURE__*/ defineChain({
    ...chainConfig,
    id: 130,
    name: 'Unichain',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://mainnet.unichain.org/'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Uniscan',
            url: 'https://uniscan.xyz',
            apiUrl: 'https://api.uniscan.xyz/api',
        },
    },
    contracts: {
        ...chainConfig.contracts,
        multicall3: {
            address: '******************************************',
            blockCreated: 0,
        },
        disputeGameFactory: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
            },
        },
    },
    sourceId,
});
//# sourceMappingURL=unichain.js.map