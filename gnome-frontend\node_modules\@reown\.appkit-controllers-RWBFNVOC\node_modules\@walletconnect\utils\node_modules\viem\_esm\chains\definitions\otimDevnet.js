import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const otimDevnet = /*#__PURE__*/ defineChain({
    id: 41144114,
    name: 'Otim Devnet',
    nativeCurrency: {
        decimals: 18,
        name: 'ETH',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: {
            http: ['http://devnet.otim.xyz'],
        },
    },
    contracts: {
        batchInvoker: {
            address: '******************************************',
        },
    },
});
//# sourceMappingURL=otimDevnet.js.map