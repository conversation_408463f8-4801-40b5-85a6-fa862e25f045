import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const kaia = /*#__PURE__*/ define<PERSON>hain({
    id: 8_217,
    name: '<PERSON><PERSON>',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: '<PERSON><PERSON><PERSON>',
    },
    rpcUrls: {
        default: { http: ['https://public-en.node.kaia.io'] },
    },
    blockExplorers: {
        default: {
            name: 'KaiaS<PERSON>',
            url: 'https://kaiascan.io',
            apiUrl: 'https://api-cypress.klaytnscope.com/api',
        },
    },
    contracts: {
        multicall3: {
            address: '0xcA11bde05977b3631167028862bE2a173976CA11',
            blockCreated: 96002415,
        },
    },
});
//# sourceMappingURL=kaia.js.map