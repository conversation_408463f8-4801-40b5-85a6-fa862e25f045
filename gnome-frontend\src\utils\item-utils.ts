// Import pickaxe images
import pickaxe1Image from '../assets/1.png';
import pickaxe2Image from '../assets/2.png';
import pickaxe3Image from '../assets/3.png';
import pickaxe4Image from '../assets/4.png';
import pickaxe5Image from '../assets/5.png';

// Import bag images
import bag1Image from '../assets/bag1.png';
import bag2Image from '../assets/bag2.png';
import bag3Image from '../assets/bag3.png';
import bag4Image from '../assets/bag4.png';

/**
 * Get bag details based on bag ID
 */
export const getBagDetails = (bagId: string) => {
  const bags: Record<string, { name: string; slots: number; price: number }> = {
    '1': { name: 'Basic Bag', slots: 4, price: 50000 },
    '2': { name: 'Elite Bag', slots: 6, price: 100000 },
    '3': { name: 'Legendary Bag', slots: 8, price: 150000 },
    '4': { name: 'Ultimate Bag', slots: 10, price: 250000 }
  };

  return bags[bagId] || bags['1']; // Default to Basic if ID not found
};

/**
 * Get pickaxe details based on pickaxe ID
 */
export const getPickaxeDetails = (pickaxeId: string) => {
  const pickaxes: Record<string, { 
    name: string; 
    speed: number; 
    bonuses: { 
      copper: number; 
      silver: number; 
      gold: number 
    }; 
    price: number 
  }> = {
    '1': { name: 'Basic Pickaxe', speed: 1.0, bonuses: { copper: 0, silver: 0, gold: 0 }, price: 50000 },
    '2': { name: 'Elite Pickaxe', speed: 1.25, bonuses: { copper: 2, silver: 3, gold: 0 }, price: 100000 },
    '3': { name: 'Legendary Pickaxe', speed: 1.5, bonuses: { copper: 4, silver: 6, gold: 0 }, price: 150000 },
    '4': { name: 'Mythical Pickaxe', speed: 2.0, bonuses: { copper: 6, silver: 9, gold: 2 }, price: 250000 },
    '5': { name: 'Divine Pickaxe', speed: 2.5, bonuses: { copper: 8, silver: 12, gold: 4 }, price: 400000 }
  };

  return pickaxes[pickaxeId] || pickaxes['1']; // Default to Basic if ID not found
};

/**
 * Get pickaxe image based on pickaxe ID
 */
export const getPickaxeImage = (pickaxeId: string) => {
  const pickaxeImages: Record<string, any> = {
    '1': pickaxe1Image,
    '2': pickaxe2Image,
    '3': pickaxe3Image,
    '4': pickaxe4Image,
    '5': pickaxe5Image
  };

  return pickaxeImages[pickaxeId] || pickaxeImages['1']; // Default to Basic if ID not found
};

/**
 * Get bag image based on bag ID
 */
export const getBagImage = (bagId: string) => {
  const bagImages: Record<string, any> = {
    '1': bag1Image,
    '2': bag2Image,
    '3': bag3Image,
    '4': bag4Image
  };

  return bagImages[bagId] || bagImages['1']; // Default to Basic if ID not found
};
