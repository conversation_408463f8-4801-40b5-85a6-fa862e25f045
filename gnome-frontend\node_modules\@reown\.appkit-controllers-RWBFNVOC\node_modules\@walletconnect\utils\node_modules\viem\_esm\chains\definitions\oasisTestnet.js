import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js';
export const oasisTestnet = /*#__PURE__*/ define<PERSON>hain({
    id: 4090,
    network: 'oasis-testnet',
    name: 'Oasis Testnet',
    nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: 'FTN', decimals: 18 },
    rpcUrls: {
        default: { http: ['https://rpc1.oasis.bahamutchain.com'] },
    },
    blockExplorers: {
        default: {
            name: 'Ftnscan',
            url: 'https://oasis.ftnscan.com',
            apiUrl: 'https://oasis.ftnscan.com/api',
        },
    },
    testnet: true,
});
//# sourceMappingURL=oasisTestnet.js.map