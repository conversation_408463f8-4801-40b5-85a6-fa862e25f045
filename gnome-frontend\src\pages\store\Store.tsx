import { useState, useEffect } from 'preact/hooks';
import { DashboardLayout } from '../../components/layout/DashboardLayout';
import { useStore } from '../../hooks/useStore';
import { getMinerProfilePicture } from '../../utils/miner-utils';



export function Store() {
  const { 
    miners, 
    isLoading, 
    error, 
    purchaseMiner, 
    isPurchasing, 
    purchaseError, 
    purchaseSuccess, 
    refetch 
  } = useStore();

  const [selectedMinerId, setSelectedMinerId] = useState<number | null>(null);

  // Clear transaction time from localStorage when component unmounts
  useEffect(() => {
    return () => {
      localStorage.removeItem('transactionSubmitTime');
    };
  }, []);

  // Automatically redirect to dashboard when purchase is successful
  useEffect(() => {
    if (purchaseSuccess) {
      // Add a small delay to show the success message before redirecting
      const redirectTimer = setTimeout(() => {
        window.location.href = '/dashboard';
      }, 1500); // 1.5 seconds delay

      return () => clearTimeout(redirectTimer);
    }
  }, [purchaseSuccess]);

  // Additional check for transaction that might be confirmed but UI not updated
  useEffect(() => {
    // If we're showing the waiting message, check if the transaction might be confirmed
    if (purchaseError === 'Purchase transaction submitted. Waiting for blockchain confirmation...') {
      console.log('Store component checking for potential transaction success');

      // Check if there's a transaction submission time in localStorage
      const transactionTime = localStorage.getItem('transactionSubmitTime');
      if (transactionTime) {
        const currentTime = new Date().getTime();
        const elapsedTime = currentTime - parseInt(transactionTime);

        // If it's been more than 20 seconds since submission, force redirect to dashboard
        if (elapsedTime > 20000) {
          console.log('Transaction likely confirmed based on elapsed time, redirecting to dashboard');
          // Clear the transaction time from localStorage
          localStorage.removeItem('transactionSubmitTime');
          // Redirect to dashboard
          window.location.href = '/dashboard';
        }
      }
    }
  }, [purchaseError]);

  // Check for pending transactions on component mount
  useEffect(() => {
    // Check if there's a transaction submission time in localStorage
    const transactionTime = localStorage.getItem('transactionSubmitTime');
    if (transactionTime) {
      const currentTime = new Date().getTime();
      const elapsedTime = currentTime - parseInt(transactionTime);

      console.log('Store component found transaction time in localStorage, elapsed time:', elapsedTime);

      // If it's been more than 25 seconds, assume the transaction is confirmed and redirect
      if (elapsedTime > 25000) {
        console.log('Transaction likely confirmed based on elapsed time on mount, redirecting to dashboard');
        // Clear the transaction time from localStorage
        localStorage.removeItem('transactionSubmitTime');
        // Redirect to dashboard
        window.location.href = '/dashboard';
      }
    }
  }, []);

  // Handle miner selection
  const handleSelectMiner = (minerId: number) => {
    setSelectedMinerId(minerId);
  };

  // Handle miner purchase
  const handlePurchaseMiner = async () => {
    if (selectedMinerId !== null) {
      try {
        await purchaseMiner(selectedMinerId);
      } catch (error) {
        console.error('Purchase error in component:', error);
      }
    }
  };

  return (
    <DashboardLayout>
      {purchaseError && (
        <div className="bg-red-800 text-white p-4 rounded-lg mb-6">
          <p className="pixelated-text">{purchaseError}</p>
          <button 
            className="bg-red-700 hover:bg-red-600 px-4 py-2 rounded mt-2 pixelated-text"
            onClick={() => {
              // Clear the transaction time from localStorage when dismissing the error
              if (purchaseError === 'Purchase transaction submitted. Waiting for blockchain confirmation...') {
                localStorage.removeItem('transactionSubmitTime');
              }
              setSelectedMinerId(null);
            }}
          >
            Dismiss
          </button>
        </div>
      )}

      {purchaseSuccess && (
        <div className="bg-green-800 text-white p-4 rounded-lg mb-6">
          <p className="pixelated-text">Miner purchased successfully!</p>
          <button 
            className="bg-green-700 hover:bg-green-600 px-4 py-2 rounded mt-2 pixelated-text"
            onClick={() => window.location.href = '/dashboard'}
          >
            Go to Dashboard
          </button>
        </div>
      )}

      <div className="bg-gradient-to-r from-[#191919] to-[#1f1f1f] rounded-lg p-8 shadow-lg mb-8">
        <div className="flex items-center gap-4 mb-6">
          <h1 className="text-3xl pixelated-text bg-clip-text text-transparent bg-gradient-to-r from-orange-400 to-yellow-400">Miner Store</h1>
          <div className="flex-1 h-px bg-gradient-to-r from-orange-400/20 to-transparent"></div>
        </div>
        <div className="flex gap-6 items-start">
          <p className="text-white/80 pixelated-text flex-1">
            Purchase miners to start your mining operations. Each miner comes with a Basic Bag and Basic Pickaxe.
            Choose wisely based on their unique abilities and traits.
          </p>
          <div className="px-4 py-2 bg-[#432433]/30 rounded-lg border border-[#432433]/50">
            <span className="text-sm pixelated-text text-orange-400/80">Available Miners: {miners.length}</span>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-96">
          <div className="text-white pixelated-text">Loading miners...</div>
        </div>
      ) : error ? (
        <div className="bg-red-800 text-white p-4 rounded-lg mb-6">
          <p className="pixelated-text">{error}</p>
          <button 
            className="bg-red-700 hover:bg-red-600 px-4 py-2 rounded mt-2 pixelated-text"
            onClick={() => refetch()}
          >
            Try Again
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {miners.map((miner) => (
            <div 
              key={miner.id} 
              className="bg-[#1b1b1b] rounded-lg p-6 mb-6 cursor-pointer"
              onClick={() => handleSelectMiner(miner.id)}
            >
              <div className="flex items-center gap-3 mb-4">
                <img
                  src={getMinerProfilePicture(miner.name)}
                  alt={miner.name}
                  className="w-8 h-8 object-contain"
                />
                <div className="flex-1">
                  <h2 className="text-base pixelated-text uppercase">{miner.name}</h2>
                  <div className="bg-[#432433] px-2 py-0.5 rounded text-xs pixelated-text inline-block">
                    {miner.price} $CAVE
                  </div>
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex items-center gap-2">
                  <span className="text-gray-400 pixelated-text text-sm w-10">Luck</span>
                  <div className="flex-1 flex gap-[2px]">
                    <div className={`h-2 flex-1 ${['low', 'moderate', 'high'].includes(miner.luckLevel.toLowerCase()) ? 'bg-[#22c55e]' : 'bg-[#1f1f1f]'}`}></div>
                    <div className={`h-2 flex-1 ${['moderate', 'high'].includes(miner.luckLevel.toLowerCase()) ? 'bg-[#22c55e]' : 'bg-[#1f1f1f]'}`}></div>
                    <div className={`h-2 flex-1 ${miner.luckLevel.toLowerCase() === 'high' ? 'bg-[#22c55e]' : 'bg-[#1f1f1f]'}`}></div>
                  </div>
                  <span className="text-gray-300 pixelated-text text-sm w-16">{miner.luckLevel.toLowerCase()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-gray-400 pixelated-text text-sm w-10">Speed</span>
                  <div className="flex-1 flex gap-[2px]">
                    <div className={`h-2 flex-1 ${['normal', 'moderate', 'high'].includes(miner.speedLevel.toLowerCase()) ? 'bg-[#22c55e]' : 'bg-[#1f1f1f]'}`}></div>
                    <div className={`h-2 flex-1 ${['moderate', 'high'].includes(miner.speedLevel.toLowerCase()) ? 'bg-[#22c55e]' : 'bg-[#1f1f1f]'}`}></div>
                    <div className={`h-2 flex-1 ${miner.speedLevel.toLowerCase() === 'high' ? 'bg-[#22c55e]' : 'bg-[#1f1f1f]'}`}></div>
                  </div>
                  <span className="text-gray-300 pixelated-text text-sm w-16">{miner.speedLevel.toLowerCase()}</span>
                </div>
              </div>

              <button
                className={`w-full px-4 py-2 rounded pixelated-text text-sm ${selectedMinerId === miner.id ? 'bg-[#432433] text-white' : 'bg-[#1f2937] text-gray-300 hover:bg-[#374151]'}`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleSelectMiner(miner.id);
                }}
              >
                {selectedMinerId === miner.id ? '✓ Selected' : 'Select Miner'}
              </button>
            </div>
          ))}
        </div>
      )}

      {selectedMinerId !== null && (
        <div className="fixed bottom-0 left-0 right-0 bg-black/95 backdrop-blur-sm p-6">
          <div className="container mx-auto flex justify-between items-center">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3 bg-[#191919] px-4 py-2 rounded-lg">
                <img
                  src={getMinerProfilePicture(miners.find(m => m.id === selectedMinerId)?.name || '')}
                  alt="Selected miner"
                  className="w-8 h-8 object-contain"
                />
                <div className="pixelated-text">
                  <div className="text-sm text-gray-400">Selected Miner</div>
                  <div>{miners.find(m => m.id === selectedMinerId)?.name}</div>
                </div>
              </div>
              <div className="bg-[#432433]/30 px-4 py-2 rounded-lg">
                <div className="text-sm text-orange-400/80 pixelated-text">Price</div>
                <div className="pixelated-text">{miners.find(m => m.id === selectedMinerId)?.price} tokens</div>
              </div>
            </div>
            <button
              className={`px-8 py-3 rounded-lg pixelated-text transition-all duration-200 ${
                isPurchasing
                  ? 'bg-green-600/50 cursor-not-allowed'
                  : 'bg-green-600 hover:bg-green-500 hover:shadow-lg hover:shadow-green-500/20'
              }`}
              onClick={handlePurchaseMiner}
              disabled={isPurchasing}
            >
              {isPurchasing ? (
                <span className="flex items-center gap-2">
                  <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  Processing...
                </span>
              ) : 'Purchase Miner'}
            </button>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}

export default Store;
