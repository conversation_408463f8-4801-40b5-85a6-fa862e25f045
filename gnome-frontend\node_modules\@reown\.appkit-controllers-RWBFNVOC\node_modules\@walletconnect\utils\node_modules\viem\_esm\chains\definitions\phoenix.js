// src/chains/definitions/phoenix.ts
import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js';
export const phoenix = /*#__PURE__*/ defineChain({
    id: 13381,
    name: 'Phoenix Blockchain',
    nativeCurrency: { name: '<PERSON>', symbol: 'PHX', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.phoenixplorer.com'],
        },
    },
    blockExplorers: {
        default: {
            name: '<PERSON><PERSON><PERSON><PERSON>',
            url: 'https://phoenixplorer.com',
            apiUrl: 'https://phoenixplorer.com/api',
        },
    },
    contracts: {
        multicall3: {
            address: '0x498cF757a575cFF2c2Ed9f532f56Efa797f86442',
            blockCreated: 5620192,
        },
    },
});
//# sourceMappingURL=phoenix.js.map