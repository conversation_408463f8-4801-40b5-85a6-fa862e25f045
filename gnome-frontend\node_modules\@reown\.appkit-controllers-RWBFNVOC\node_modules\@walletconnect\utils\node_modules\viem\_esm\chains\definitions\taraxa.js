import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const taraxa = /*#__PURE__*/ defineChain({
    id: 841,
    name: 'Taraxa Mainnet',
    nativeCurrency: { name: '<PERSON>', symbol: 'TARA', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.mainnet.taraxa.io'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Taraxa Explorer',
            url: 'https://explorer.mainnet.taraxa.io',
        },
    },
});
//# sourceMappingURL=taraxa.js.map