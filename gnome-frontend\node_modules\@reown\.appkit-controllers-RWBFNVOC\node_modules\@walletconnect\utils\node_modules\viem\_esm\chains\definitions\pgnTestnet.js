import { formatters } from '../../op-stack/formatters.js';
import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
const sourceId = 11_155_111; // sepolia
export const pgnTestnet = /*#__PURE__*/ defineChain({
    id: 58008,
    network: 'pgn-testnet',
    name: 'PGN ',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://sepolia.publicgoods.network'],
        },
    },
    blockExplorers: {
        default: {
            name: 'PGN Testnet Explorer',
            url: 'https://explorer.sepolia.publicgoods.network',
            apiUrl: 'https://explorer.sepolia.publicgoods.network/api',
        },
    },
    contracts: {
        l2OutputOracle: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        multicall3: {
            address: '******************************************',
            blockCreated: 3754925,
        },
    },
    formatters,
    sourceId,
    testnet: true,
});
//# sourceMappingURL=pgnTestnet.js.map