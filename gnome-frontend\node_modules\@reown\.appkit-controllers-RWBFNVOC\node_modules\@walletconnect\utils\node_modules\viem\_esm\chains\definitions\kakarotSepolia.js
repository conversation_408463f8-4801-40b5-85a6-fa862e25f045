import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js';
export const kakarotSepolia = /*#__PURE__*/ define<PERSON>hain({
    id: 1802203764,
    name: 'Kakar<PERSON> Sepolia',
    nativeCurrency: {
        name: '<PERSON><PERSON>',
        symbol: 'ETH',
        decimals: 18,
    },
    rpcUrls: {
        default: {
            http: ['https://sepolia-rpc.kakarot.org'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Kakarot Scan',
            url: 'https://sepolia.kakarotscan.org',
        },
    },
    testnet: true,
});
//# sourceMappingURL=kakarotSepolia.js.map