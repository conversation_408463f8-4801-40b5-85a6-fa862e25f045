import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const sonic = /*#__PURE__*/ defineChain({
    id: 146,
    name: '<PERSON>',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON>',
        symbol: 'S',
    },
    rpcUrls: {
        default: { http: ['https://rpc.soniclabs.com'] },
    },
    blockExplorers: {
        default: {
            name: 'Sonic Explorer',
            url: 'https://sonicscan.org/',
        },
    },
    contracts: {
        multicall3: {
            address: '0xca11bde05977b3631167028862be2a173976ca11',
            blockCreated: 60,
        },
    },
    testnet: false,
});
//# sourceMappingURL=sonic.js.map