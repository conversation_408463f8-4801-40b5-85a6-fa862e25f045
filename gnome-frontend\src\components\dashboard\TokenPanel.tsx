import coinstackIcon from '../../assets/Coinstack.png';

interface TokenPanelProps {
  smeltingData: {
    totalCoins: number;
    smeltingOres: {
      copper: number;
      silver: number;
      gold: number;
    };
    smeltedOres: {
      copper: number;
      silver: number;
      gold: number;
    };
    totalClaimedCoins: number;
  };
  smeltingActionLoading: boolean;
  onClaimClick: () => void;
}

export function TokenPanel({
  smeltingData,
  smeltingActionLoading,
  onClaimClick
}: TokenPanelProps) {
  const smeltingTotal = smeltingData.smeltingOres.copper + smeltingData.smeltingOres.silver + smeltingData.smeltingOres.gold;
  const smeltedTotal = smeltingData.smeltedOres.copper + smeltingData.smeltedOres.silver + smeltingData.smeltedOres.gold;

  return (
    <div className="bg-[#222222] rounded-lg p-5 shadow-lg">
      <div className="flex items-center gap-2 mb-4">
        <img src={coinstackIcon} alt="Tokens" className="w-6 h-6" />
        <h3 className="text-lg pixelated-text text-white">Token Management</h3>
      </div>

      {/* Token stats with improved visuals */}
      <div className="bg-[#191919] p-3 rounded-lg mb-4">
        {/* Available coins with highlight */}
        <div className="flex justify-between items-center p-2 mb-3 bg-[#432433] rounded-lg">
          <span className="pixelated-text text-white">Claimable $CAVE:</span>
          <span className="text-yellow-500 pixelated-text font-bold text-lg">{smeltingData.totalCoins}</span>
        </div>

        <div className="space-y-3">
          {/* Smelting ores */}
          <style jsx>{`
            @keyframes smelting-bounce {
              0%, 100% { transform: translateY(0); }
              50% { transform: translateY(-4px); }
            }
            @keyframes fire-flicker {
              0%, 100% { transform: scale(1); opacity: 1; }
              50% { transform: scale(1.2); opacity: 0.8; }
            }
            @keyframes sparkle-spin {
              0% { transform: rotate(0deg) scale(1); }
              50% { transform: rotate(180deg) scale(1.2); }
              100% { transform: rotate(360deg) scale(1); }
            }
            .smelting-container {
              animation: smelting-bounce 1s ease-in-out infinite;
            }
            .fire-effect {
              animation: fire-flicker 0.5s ease-in-out infinite;
            }
            .sparkle-effect {
              animation: sparkle-spin 1.5s linear infinite;
            }
          `}</style>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <span className="pixelated-text text-white">On Progress Smelting Ores:</span>
              {smeltingTotal > 0 && (
                <div className="flex items-center gap-1 smelting-container">
                  <div className="fire-effect text-orange-500">🔥</div>
                  <div className="relative">
                    <div className="sparkle-effect absolute -top-2 left-0 text-yellow-500">✨</div>
                    <div className="sparkle-effect absolute -top-1 right-0 text-yellow-500" style={{ animationDelay: '0.5s' }}>✨</div>
                    <div className="text-orange-600 transform scale-125">⚗️</div>
                  </div>
                </div>
              )}
            </div>
            <span className="text-orange-500 pixelated-text">{smeltingTotal}</span>
          </div>

          {/* Smelted ores */}
          <div className="flex justify-between items-center">
            <span className="pixelated-text text-white">Total Smelted Ores:</span>
            <span className="text-green-500 pixelated-text">{smeltedTotal}</span>
          </div>

          {/* Total claimed */}
          <div className="flex justify-between items-center">
            <span className="pixelated-text text-white">Total $CAVE Claimed:</span>
            <span className="text-green-500 pixelated-text">{smeltingData.totalClaimedCoins}</span>
          </div>
        </div>
      </div>

      {/* Claim button with improved styling */}
      <button
        className={`w-full pixelated-text py-2 px-4 rounded mb-4 ${smeltingData.totalCoins <= 0 ? 'bg-gray-700 text-gray-400 cursor-not-allowed' : 'bg-yellow-600 hover:bg-yellow-700 text-white'}`}
        onClick={onClaimClick}
        disabled={smeltingActionLoading || smeltingData.totalCoins <= 0}
      >
        {smeltingActionLoading ? 'Processing...' : 'Claim Tokens'}
      </button>

      <div className="bg-[#191919] p-3 rounded-lg">
        <p className="text-xs pixelated-text text-gray-400">
          Claim your coins as $CAVE tokens that can be used in the game or traded on exchanges.
        </p>
      </div>
    </div>
  );
}
