interface TimeResult {
  text: string;
  shouldRefresh: boolean;
}

/**
 * Format time remaining for countdown and return additional info about completion
 * @param mining Mining operation data
 * @returns Formatted time string and refresh status
 */
export const formatTimeRemaining = (mining: any): TimeResult => {
  // If mining is not in progress, return '-'
  if (mining.mining.status !== 'IN_PROGRESS') return { text: '-', shouldRefresh: false };

  // If endedAt is not set, return '-'
  if (!mining.mining.endedAt) return { text: '-', shouldRefresh: false };

  // Convert endedAt to UTC timestamp
  const end = Date.parse(mining.mining.endedAt);
  const now = Date.now(); // UTC timestamp
  const diff = end - now;

  // If time is up, immediately return 0
  if (diff <= 0) {
    return { text: '0m 0s', shouldRefresh: true };
  }

  // Calculate hours, minutes, and seconds
  const totalMinutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);

  // Format based on duration
  if (hours > 0) {
    return { text: `${hours}h ${minutes}m ${seconds}s`, shouldRefresh: false };
  } else {
    return { text: `${minutes}m ${seconds}s`, shouldRefresh: false };
  }
};
