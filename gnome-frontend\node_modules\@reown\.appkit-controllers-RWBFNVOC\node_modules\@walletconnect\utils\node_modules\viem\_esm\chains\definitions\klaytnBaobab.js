import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const klaytnBaobab = /*#__PURE__*/ defineChain({
    id: 1_001,
    name: 'Klaytn Baobab Testnet',
    network: 'klaytn-baobab',
    nativeCurrency: {
        decimals: 18,
        name: 'Ba<PERSON>ab Klaytn',
        symbol: 'KLAY',
    },
    rpcUrls: {
        default: { http: ['https://public-en-baobab.klaytn.net'] },
    },
    blockExplorers: {
        default: {
            name: 'KlaytnScope',
            url: 'https://baobab.klaytnscope.com',
        },
    },
    contracts: {
        multicall3: {
            address: '0xcA11bde05977b3631167028862bE2a173976CA11',
            blockCreated: 123390593,
        },
    },
    testnet: true,
});
//# sourceMappingURL=klaytnBaobab.js.map