import { chainConfig } from '../../op-stack/chainConfig.js';
import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
const sourceId = 11_155_111; // sepolia
export const modeTestnet = /*#__PURE__*/ defineChain({
    ...chainConfig,
    id: 919,
    name: 'Mode Testnet',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://sepolia.mode.network'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Blockscout',
            url: 'https://sepolia.explorer.mode.network',
            apiUrl: 'https://sepolia.explorer.mode.network/api',
        },
    },
    contracts: {
        ...chainConfig.contracts,
        l2OutputOracle: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 3778393,
            },
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 3778395,
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 3778392,
            },
        },
        multicall3: {
            address: '0xBAba8373113Fb7a68f195deF18732e01aF8eDfCF',
            blockCreated: 3019007,
        },
    },
    testnet: true,
    sourceId,
});
//# sourceMappingURL=modeTestnet.js.map