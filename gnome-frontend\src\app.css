/* Reset any default styles that might interfere with our layout */
body, html {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  font-family: 'VT323', monospace;
  font-size: 18px;
  letter-spacing: 0.5px;
}

#app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Custom styles for the Cave app */
.connect-wallet-btn {
  border: 2px solid white;
  background-color: transparent;
  color: white;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.connect-wallet-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Logo styles for compatibility */
.logo {
  height: 6em;
  padding: 1.5em;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.preact:hover {
  filter: drop-shadow(0 0 2em #673ab8aa);
}

/* Custom Tooltip */
.tooltip-container {
  position: relative;
}

.tooltip-container:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

.tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.5rem;
  background-color: #222222;
  border: 1px solid #432433;
  color: white;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 50;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Toast Animation */
@keyframes fade-in-down {
  0% {
    opacity: 0;
    transform: translate(-50%, -2rem);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

.animate-fade-in-down {
  animation: fade-in-down 0.3s ease-out;
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #432433;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #532d3e;
}

/* Custom styles for Slick Slider */
.slick-track {
  display: flex !important;
  gap: 1rem;
}

.slick-slide {
  height: inherit !important;
}

.slick-slide > div {
  height: 100%;
}

/* Custom Arrow Styles */
.slick-prev,
.slick-next {
  width: 32px !important;
  height: 32px !important;
  background: #432433 !important;
  border: 2px solid #532d3e !important;
  border-radius: 8px !important;
  z-index: 1 !important;
  transition: all 0.2s ease !important;
}

.slick-prev:hover,
.slick-next:hover {
  background: #532d3e !important;
  border-color: #63354a !important;
}

.slick-prev {
  left: -5px !important;
}

.slick-next {
  right: -5px !important;
}

.slick-prev:before,
.slick-next:before {
  font-size: 16px !important;
  opacity: 1 !important;
}

.slick-prev:before {
  content: '<<' !important;
  font-weight: bold !important;
  letter-spacing: -2px !important;
}

.slick-next:before {
  content: '>>' !important;
  font-weight: bold !important;
  letter-spacing: -2px !important;
}

.slick-disabled {
  opacity: 0 !important;
  cursor: default !important;
  pointer-events: none !important;
}
