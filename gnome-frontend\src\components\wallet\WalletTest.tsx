import { useAccount, useConnect, useDisconnect } from 'wagmi';
import { useAuth } from '../../context/AuthContext';
import { isMobile, getAvailableWallets } from '../../utils/wallet-utils';

export function WalletTest() {
  const { address, isConnected } = useAccount();
  const { connectors, connect, isPending } = useConnect();
  const { disconnect } = useDisconnect();
  const { isAuthenticated, login, logout, isLoading, error } = useAuth();

  const availableWallets = getAvailableWallets();

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px' }}>
      <h3>Wallet Connection Test</h3>
      
      <div>
        <strong>Environment:</strong> {isMobile() ? 'Mobile' : 'Desktop'}
      </div>
      
      <div>
        <strong>Connection Status:</strong> {isConnected ? 'Connected' : 'Disconnected'}
      </div>
      
      <div>
        <strong>Authentication Status:</strong> {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
      </div>
      
      {address && (
        <div>
          <strong>Address:</strong> {address}
        </div>
      )}
      
      {error && (
        <div style={{ color: 'red' }}>
          <strong>Error:</strong> {error}
        </div>
      )}
      
      <div>
        <strong>Available Wallets:</strong>
        <ul>
          {availableWallets.map((wallet, index) => (
            <li key={index}>{wallet.name} ({wallet.type})</li>
          ))}
        </ul>
      </div>
      
      <div>
        <strong>Wagmi Connectors:</strong>
        <ul>
          {connectors.map((connector) => (
            <li key={connector.id}>
              {connector.name} (ID: {connector.id})
              <button
                onClick={() => connect({ connector })}
                disabled={isPending || isConnected}
                style={{ marginLeft: '10px' }}
              >
                Connect
              </button>
            </li>
          ))}
        </ul>
      </div>
      
      <div style={{ marginTop: '20px' }}>
        {!isConnected && (
          <button onClick={() => connect({ connector: connectors[0] })}>
            Quick Connect (First Connector)
          </button>
        )}
        
        {isConnected && !isAuthenticated && (
          <button onClick={login} disabled={isLoading}>
            {isLoading ? 'Signing...' : 'Sign Message'}
          </button>
        )}
        
        {isConnected && (
          <button onClick={() => disconnect()} style={{ marginLeft: '10px' }}>
            Disconnect
          </button>
        )}
        
        {isAuthenticated && (
          <button onClick={logout} style={{ marginLeft: '10px' }}>
            Logout
          </button>
        )}
      </div>
      
      <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        <strong>Debug Info:</strong>
        <pre>{JSON.stringify({
          isConnected,
          isAuthenticated,
          isLoading,
          isPending,
          hasEthereum: !!window.ethereum,
          userAgent: navigator.userAgent.substring(0, 100) + '...'
        }, null, 2)}</pre>
      </div>
    </div>
  );
}
