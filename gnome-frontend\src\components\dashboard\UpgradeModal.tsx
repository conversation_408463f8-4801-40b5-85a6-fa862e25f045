import { getBagDetails, getPickaxeDetails, getPickaxeImage, getBagImage } from '../../utils/item-utils';
import upgradeIcon from '../../assets/upgradeicon.png';

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  upgradeType: 'bag' | 'pickaxe';
  selectedMining: {
    bag: { id: string };
    pickaxe: { id: string };
  };
  isUpgrading: boolean;
  upgradeError: string | null;
  upgradeSuccess?: boolean;
}

export function UpgradeModal({
  isOpen,
  onClose,
  onConfirm,
  upgradeType,
  selectedMining,
  isUpgrading,
  upgradeError,
  upgradeSuccess
}: UpgradeModalProps) {
  if (!isOpen) return null;

  const getUpgradeDetails = () => {
    if (upgradeType === 'bag') {
      const nextBag = getBagDetails((parseInt(selectedMining.bag.id) + 1).toString());
      return {
        name: nextBag.name,
        price: nextBag.price
      };
    } else {
      const nextPickaxe = getPickaxeDetails((parseInt(selectedMining.pickaxe.id) + 1).toString());
      return {
        name: nextPickaxe.name,
        price: nextPickaxe.price
      };
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
      <div className="bg-[#222222] rounded-lg p-6 shadow-lg max-w-md w-full">
        <h2 className="text-xl pixelated-text mb-4">Upgrade {upgradeType === 'bag' ? 'Bag' : 'Pickaxe'}</h2>

        <div className="bg-[#191919] p-4 rounded-lg mb-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <img 
                src={upgradeType === 'pickaxe' 
                  ? getPickaxeImage((parseInt(selectedMining.pickaxe.id) + 1).toString())
                  : upgradeType === 'bag'
                  ? getBagImage((parseInt(selectedMining.bag.id) + 1).toString())
                  : upgradeIcon
                } 
                alt={upgradeType === 'pickaxe' ? 'Pickaxe' : upgradeType === 'bag' ? 'Bag' : 'Upgrade'} 
                className="w-6 h-6 object-contain" 
              />
              <span className="pixelated-text text-white">{getUpgradeDetails().name}</span>
            </div>
            <span className="bg-[#432433] px-3 py-1 rounded-full text-sm pixelated-text">
              {getUpgradeDetails().price} tokens
            </span>
          </div>

          {upgradeType === 'bag' && (
            <p className="text-sm pixelated-text text-gray-400">
              Increases bag capacity to {getBagDetails((parseInt(selectedMining.bag.id) + 1).toString()).slots} slots
            </p>
          )}

          {upgradeType === 'pickaxe' && (
            <div className="text-sm pixelated-text text-gray-400">
              <p>Increases mining speed to {getPickaxeDetails((parseInt(selectedMining.pickaxe.id) + 1).toString()).speed}x</p>
              {getPickaxeDetails((parseInt(selectedMining.pickaxe.id) + 1).toString()).bonuses.copper > 0 && (
                <p>+{getPickaxeDetails((parseInt(selectedMining.pickaxe.id) + 1).toString()).bonuses.copper}% Copper bonus</p>
              )}
              {getPickaxeDetails((parseInt(selectedMining.pickaxe.id) + 1).toString()).bonuses.silver > 0 && (
                <p>+{getPickaxeDetails((parseInt(selectedMining.pickaxe.id) + 1).toString()).bonuses.silver}% Silver bonus</p>
              )}
              {getPickaxeDetails((parseInt(selectedMining.pickaxe.id) + 1).toString()).bonuses.gold > 0 && (
                <p>+{getPickaxeDetails((parseInt(selectedMining.pickaxe.id) + 1).toString()).bonuses.gold}% Gold bonus</p>
              )}
            </div>
          )}
        </div>

        {upgradeError && (
          <div className="bg-red-800 text-white p-4 rounded-lg mb-4">
            <p className="pixelated-text">{upgradeError}</p>
          </div>
        )}

        {upgradeSuccess && (
          <div className="bg-green-800 text-white p-4 rounded-lg mb-4">
            <p className="pixelated-text">Upgrade successful!</p>
          </div>
        )}

        <div className="flex justify-between">
          <button 
            className="bg-[#333333] hover:bg-[#444444] px-4 py-2 rounded pixelated-text"
            onClick={onClose}
            disabled={isUpgrading}
          >
            Cancel
          </button>
          <button 
            className="bg-[#432433] hover:bg-[#532d3e] px-4 py-2 rounded pixelated-text"
            onClick={onConfirm}
            disabled={isUpgrading || upgradeSuccess}
          >
            {isUpgrading ? 'Processing...' : upgradeSuccess ? 'Upgraded!' : 'Confirm Upgrade'}
          </button>
        </div>
      </div>
    </div>
  );
}
