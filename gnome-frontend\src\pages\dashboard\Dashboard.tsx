import { useState, useEffect } from 'preact/hooks';
import { formatTimeRemaining } from '../../utils/time-utils';
import { useWriteContract, useWaitForTransactionReceipt } from 'wagmi';
import { DashboardLayout } from '../../components/layout/DashboardLayout';
import { useMiningData } from '../../hooks/useMiningData';
import { useUpgrade } from '../../hooks/useUpgrade';
import miningApi from '../../services/mining';
import Slider from 'react-slick'; 
import 'slick-carousel/slick/slick.css'; 
import 'slick-carousel/slick/slick-theme.css'; 
import copperImage from '../../assets/Copper.png';
import silverImage from '../../assets/Silver.png';
import goldImage from '../../assets/Gold.png';
import { UpgradeModal } from '../../components/dashboard/UpgradeModal';
import { ClaimModal } from '../../components/dashboard/ClaimModal';
import { SuccessModal } from '../../components/dashboard/SuccessModal';
import { ErrorModal } from '../../components/dashboard/ErrorModal';
import { ResourcesPanel } from '../../components/dashboard/ResourcesPanel';
import { SmeltingPanel } from '../../components/dashboard/SmeltingPanel';
import { TokenPanel } from '../../components/dashboard/TokenPanel';
import { EmptyMinerCard } from '../../components/dashboard/EmptyMinerCard';
import { MinersGridModal } from '../../components/dashboard/MinersGridModal';
import upgradeIcon from '../../assets/upgradeicon.png';

// Import item utilities
import { getBagDetails, getPickaxeDetails, getPickaxeImage, getBagImage } from '../../utils/item-utils';

// Import miner utilities
import { getMinerImage, getMinerProfilePicture } from '../../utils/miner-utils';

// Slider settings
const sliderSettings = {
  dots: false,
  infinite: false,
  speed: 500,
  slidesToShow: 3,
  slidesToScroll: 1,
  arrows: true,
  responsive: [
    {
      breakpoint: 1280,
      settings: {
        slidesToShow: 2
      }
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        arrows: true,
        slidesToScroll: 1
      }
    }
  ]
};

export function Dashboard() {
  const { minings, stacks, isLoading, error, refetch } = useMiningData();
  const { upgradeBag, upgradePickaxe, isUpgrading, upgradeError, upgradeSuccess, resetUpgradeState } = useUpgrade();
  const [miningActionLoading, setMiningActionLoading] = useState<{[key: string]: boolean}>({});
  const [actionError, setActionError] = useState<string | null>(null);
  const [countdowns, setCountdowns] = useState<{[key: string]: string}>({});
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [selectedMining, setSelectedMining] = useState<any>(null);
  const [upgradeType, setUpgradeType] = useState<'bag' | 'pickaxe' | null>(null);

  // Smelting state
  const [smeltingData, setSmeltingData] = useState<{
    smeltingOres: { copper: number; silver: number; gold: number };
    smeltedOres: { copper: number; silver: number; gold: number };
    totalCoins: number;
    totalClaimedCoins: number;
  }>({
    smeltingOres: { copper: 0, silver: 0, gold: 0 },
    smeltedOres: { copper: 0, silver: 0, gold: 0 },
    totalCoins: 0,
    totalClaimedCoins: 0
  });
  const [smeltingActionLoading, setSmeltingActionLoading] = useState(false);
  const [claimAmount, setClaimAmount] = useState<string>('');
  const [showClaimModal, setShowClaimModal] = useState(false);
  
  // Success modal state
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Global stats from API
  const [globalStats, setGlobalStats] = useState<{
    totalOres: {
      copper: number;
      silver: number;
      gold: number;
      total: number;
    };
    totalCoins: number;
    totalClaimedCoins: number;
  }>({ 
    totalOres: {
      copper: 0,
      silver: 0,
      gold: 0,
      total: 0
    },
    totalCoins: 0,
    totalClaimedCoins: 0
  });

  // Removed unused mined variable

  // No longer needed with the polling approach

  // Update countdowns every second
  useEffect(() => {
    const updateCountdowns = () => {
      const newCountdowns: {[key: string]: string} = {};

      minings.forEach(mining => {
        const result = formatTimeRemaining(mining);
        newCountdowns[mining.id] = result.text;
      });

      setCountdowns(newCountdowns);
    };

    // Update immediately
    updateCountdowns();

    // Then update every second
    const interval = setInterval(updateCountdowns, 1000);

    return () => clearInterval(interval);
  }, [minings]);

  // Track which mining operations we've already refreshed for
  const [refreshedMiningIds, setRefreshedMiningIds] = useState<{[key: string]: boolean}>({});

  // Auto-refresh when an upgrade is successful
  useEffect(() => {
    if (upgradeSuccess) {
      console.log('Upgrade successful, refreshing dashboard...');
      refetch();
      setShowUpgradeModal(false);
      resetUpgradeState();
    }
  }, [upgradeSuccess, refetch, resetUpgradeState]);

  // One-time refresh for completed mining operations
  useEffect(() => {
    // Function to check if any mining operations need a refresh
    const checkForCompletedMining = () => {
      // Find mining operations that need a refresh and haven't been refreshed yet
      const completedMiningIds = minings
        .filter(mining => {
          // Check if mining is in progress but timer has expired
          if (mining.mining.status === 'IN_PROGRESS' && mining.mining.endedAt) {
            const endTime = new Date(mining.mining.endedAt).getTime();
            const now = new Date().getTime();
            // Only consider it if we haven't refreshed for this mining ID yet
            return endTime <= now && !refreshedMiningIds[mining.id];
          }
          return false;
        })
        .map(mining => mining.id);

      if (completedMiningIds.length > 0) {
        console.log('Found new completed mining operations:', completedMiningIds);

        // Mark these mining IDs as refreshed
        const newRefreshedIds = {...refreshedMiningIds};
        completedMiningIds.forEach(id => {
          newRefreshedIds[id] = true;
        });
        setRefreshedMiningIds(newRefreshedIds);

        // Refresh the data
        console.log('Refreshing data for completed mining operations...');
        refetch();
      }
    };

    // Only check if we have mining operations
    if (minings.length > 0) {
      checkForCompletedMining();
    }

    // Set up a regular but less frequent polling interval (every 30 seconds)
    const pollingInterval = setInterval(() => {
      if (minings.length > 0) {
        console.log('Periodic check for mining updates...');
        checkForCompletedMining();
      }
    }, 30000); // 30 second interval

    return () => clearInterval(pollingInterval);
  }, [minings, refreshedMiningIds, refetch]);

  // Start mining operation
  const handleStartMining = async (miningId: string) => {
    try {
      setActionError(null);
      setMiningActionLoading(prev => ({ ...prev, [miningId]: true }));

      await miningApi.startMining(miningId);
      await refetch(); // Refresh data after action
    } catch (error: any) {
      console.error('Failed to start mining:', error);
      setActionError(error.response?.data?.message || 'Failed to start mining');
    } finally {
      setMiningActionLoading(prev => ({ ...prev, [miningId]: false }));
    }
  };

  // Stop mining operation
  const handleStopMining = async (miningId: string) => {
    try {
      setActionError(null);
      setMiningActionLoading(prev => ({ ...prev, [miningId]: true }));

      await miningApi.stopMining(miningId);
      await refetch(); // Refresh data after action
    } catch (error: any) {
      console.error('Failed to stop mining:', error);
      setActionError(error.response?.data?.message || 'Failed to stop mining');
    } finally {
      setMiningActionLoading(prev => ({ ...prev, [miningId]: false }));
    }
  };

  // Stack mining operation (harvest ores)
  const handleStackMining = async (miningId: string) => {
    try {
      setActionError(null);
      setMiningActionLoading(prev => ({ ...prev, [miningId]: true }));

      await miningApi.stackMining(miningId);
      await refetch(); // Refresh data after action

      // Show success modal
      setSuccessMessage('Harvest Successful!');
      setShowSuccessModal(true);

      // Auto-hide success modal after 3 seconds
      setTimeout(() => {
        setShowSuccessModal(false);
      }, 3000);
    } catch (error: any) {
      console.error('Failed to harvest ores:', error);
      setActionError(error.response?.data?.message || 'Failed to harvest ores');
    } finally {
      setMiningActionLoading(prev => ({ ...prev, [miningId]: false }));
    }
  };

  // Stack all mining operations that are ready to smelt (bulk harvest)
  const handleStackAllMinings = async () => {
    try {
      setActionError(null);
      // Set loading state for all miners that are ready to harvest
      const readyMiners = minings.filter(mining => mining.mining.status === 'READY_TO_SMELT');
      if (readyMiners.length === 0) {
        setActionError('No miners ready to harvest');
        return;
      }
      
      // Set loading state for all miners that are ready to harvest
      const newLoadingState = {...miningActionLoading};
      readyMiners.forEach(mining => {
        newLoadingState[mining.id] = true;
      });
      setMiningActionLoading(newLoadingState);

      // Call the API to stack all mining operations
      await miningApi.stackAllMinings();
      await refetch(); // Refresh data after action

      // Show success modal
      setSuccessMessage(`Bulk Harvest Successful! (${readyMiners.length} miners)`);
      setShowSuccessModal(true);

      // Auto-hide success modal after 3 seconds
      setTimeout(() => {
        setShowSuccessModal(false);
      }, 3000);
    } catch (error: any) {
      console.error('Failed to bulk harvest ores:', error);
      setActionError(error.response?.data?.message || 'Failed to bulk harvest ores');
    } finally {
      // Reset loading state
      const resetLoadingState = {...miningActionLoading};
      minings.forEach(mining => {
        resetLoadingState[mining.id] = false;
      });
      setMiningActionLoading(resetLoadingState);
    }
  };

  // Check if mining is in progress based on API response structure
  const isMiningInProgress = (mining: any) => {
    // Check the mining status from the API response
    if (mining.mining && mining.mining.status) {
      const isInProgress = mining.mining.status === 'IN_PROGRESS';
      console.log(`Mining ${mining.id} status: ${mining.mining.status}, isInProgress: ${isInProgress}`);
      return isInProgress;
    }

    // If the status is not explicitly set, check if there's an active mining session
    // by looking at the startedAt and endedAt timestamps
    if (mining.mining.startedAt && !mining.mining.endedAt) {
      console.log(`Mining ${mining.id} has startedAt but no endedAt, considering in progress`);
      return true;
    }

    // If there's an endedAt timestamp in the future, mining is still in progress
    if (mining.mining.endedAt) {
      const endTime = new Date(mining.mining.endedAt).getTime();
      const now = new Date().getTime();
      const isInProgress = endTime > now;
      console.log(`Mining ${mining.id} endTime: ${endTime}, now: ${now}, isInProgress: ${isInProgress}`);
      return isInProgress;
    }

    console.log(`Mining ${mining.id} no clear status, returning false`);
    return false;
  };

  // Get the mining status text
  const getMiningStatusText = (mining: any) => {
    if (!mining.mining) return 'Unknown';

    if (mining.mining.status === 'READY_TO_SMELT') {
      return 'Mining Complete';
    } else if (mining.mining.status === 'IN_PROGRESS') {
      return 'Mining in Progress';
    } else if (mining.mining.status === 'IDLE') {
      return 'Idle';
    }

    return mining.mining.status || 'Unknown';
  };

  // Clear action error
  const clearActionError = () => {
    setActionError(null);
  };

  // Handle upgrade confirmation
  const handleUpgradeConfirm = () => {
    if (!selectedMining || !upgradeType) return;

    if (upgradeType === 'bag') {
      // Calculate next bag ID (current + 1)
      const nextBagId = parseInt(selectedMining.bag.id) + 1;
      upgradeBag(selectedMining.id, nextBagId);
    } else if (upgradeType === 'pickaxe') {
      // Calculate next pickaxe ID (current + 1)
      const nextPickaxeId = parseInt(selectedMining.pickaxe.id) + 1;
      upgradePickaxe(selectedMining.id, nextPickaxeId);
    }
  };

  // Close upgrade modal
  const closeUpgradeModal = () => {
    setShowUpgradeModal(false);
    setSelectedMining(null);
    setUpgradeType(null);
    resetUpgradeState();
  };

  // Fetch smelting data
  const fetchSmeltingData = async () => {
    try {
      setActionError(null);
      const data = await miningApi.getSmeltingData();
      setSmeltingData(data);
    } catch (error: any) {
      console.error('Failed to fetch smelting data:', error);
      setActionError(error.response?.data?.message || 'Failed to fetch smelting data');
    }
  };

  // State for smelting success modal
  const [showSmeltSuccessModal, setShowSmeltSuccessModal] = useState(false);
  
  // State for miners grid modal
  const [showMinersGridModal, setShowMinersGridModal] = useState(false);

  // Smelt all stacked ores
  const handleSmeltAllStacks = async () => {
    try {
      setActionError(null);
      setSmeltingActionLoading(true);
      await miningApi.smeltAllStacks();
      await fetchSmeltingData(); // Refresh smelting data
      await refetch(); // Refresh mining data
      
      // Refresh global stats
      try {
        const data = await miningApi.getGlobalStats();
        
        // Calculate total ores by type (smelting + smelted)
        const totalCopper = data.smeltingOres.copper + data.smeltedOres.copper;
        const totalSilver = data.smeltingOres.silver + data.smeltedOres.silver;
        const totalGold = data.smeltingOres.gold + data.smeltedOres.gold;
        const totalOresSum = totalCopper + totalSilver + totalGold;
        
        setGlobalStats({
          totalOres: {
            copper: totalCopper,
            silver: totalSilver,
            gold: totalGold,
            total: totalOresSum
          },
          totalCoins: data.totalCoins,
          totalClaimedCoins: data.totalClaimedCoins
        });
      } catch (error) {
        console.error('Failed to fetch global stats:', error);
      }
      setShowSmeltSuccessModal(true);
    } catch (error: any) {
      console.error('Failed to smelt stacked ores:', error);
      // Use the exact error message from the API if available
      setActionError(error.response?.data?.message || 'Failed to smelt stacked ores');
    } finally {
      setSmeltingActionLoading(false);
    }
  };

  // Use Wagmi hooks for contract interaction
  const { writeContract, isPending: isWriteLoading, data: writeData } = useWriteContract();
  
  // Wait for transaction to complete
  const { error: transactionError, isSuccess: isTransactionSuccess } = useWaitForTransactionReceipt({
    hash: writeData,
  });

  // Claim tokens
  const handleClaimTokens = async (amount?: number) => {
    try {
      setActionError(null);
      setSmeltingActionLoading(true);
      
      // STEP 1: Call the backend to get transaction details
      setActionError('Preparing transaction...');
      const transactionData = await miningApi.claimTokens(amount);
      
      // Extract transaction parameters
      const { arguments: args, signature, payer } = transactionData;
      const { orderId, amount: tokenAmount, token } = args;
      
      // Format parameters
      const formattedToken = token.toLowerCase() as `0x${string}`;
      const formattedReceiver = payer.toLowerCase() as `0x${string}`;
      const formattedSignature = signature.startsWith('0x') ? signature : `0x${signature}`;
      const amountBigInt = BigInt(tokenAmount);
      
      // STEP 2: Call the claimToken function on the contract
      setActionError('Please confirm the claim transaction in your wallet...');
      
      // Use the same payment gateway address as in other hooks
      const PAYMENT_GATEWAY_ADDRESS = import.meta.env.VITE_PAYMENT_GATEWAY_ADDRESS as `0x${string}`;
      const { paymentGatewayAbi } = await import('../../services/payment-gateway.abi');
      
      // Call the claimToken function with the correct parameters using the Wagmi hook
      writeContract({
        address: PAYMENT_GATEWAY_ADDRESS,
        abi: paymentGatewayAbi,
        functionName: 'claimToken', // This function exists in the ABI at line 267
        args: [
          orderId,
          amountBigInt,
          formattedToken,
          formattedReceiver,
          formattedSignature
        ]
      });
      
      // Close the modal after successful submission
      setShowClaimModal(false);
      setClaimAmount('');
    } catch (error: any) {
      console.error('Failed to claim tokens:', error);
      
      // Determine the appropriate error message
      let errorMessage = 'Failed to claim tokens';
      
      if (error.code === 4001) {
        // User rejected the transaction in MetaMask
        errorMessage = 'Transaction was rejected in your wallet';
      } else if (error.response?.data?.message) {
        // Backend API error
        errorMessage = error.response.data.message;
      } else if (error.message) {
        // General error with message
        errorMessage = error.message;
      }
      
      setActionError(errorMessage);
    } finally {
      setSmeltingActionLoading(false);
    }
  };
  
  // Primary effect to handle transaction status changes
  useEffect(() => {
    if (isWriteLoading) {
      setActionError('Transaction pending in your wallet...');
    } else if (transactionError) {
      setActionError(`Transaction failed: ${transactionError.message}`);
      setSmeltingActionLoading(false);
    } else if (writeData) {
      // Transaction has been submitted to the blockchain
      console.log('Transaction submitted:', writeData);
      setActionError('Transaction submitted. Waiting for confirmation...');
      
      // Set a timeout to refresh data after 15 seconds regardless of confirmation status
      setTimeout(async () => {
        console.log('Refreshing data after timeout');
        await fetchSmeltingData();
        
        // Refresh global stats
        try {
          const data = await miningApi.getGlobalStats();
          
          // Calculate total ores by type (smelting + smelted)
          const totalCopper = data.smeltingOres.copper + data.smeltedOres.copper;
          const totalSilver = data.smeltingOres.silver + data.smeltedOres.silver;
          const totalGold = data.smeltingOres.gold + data.smeltedOres.gold;
          const totalOresSum = totalCopper + totalSilver + totalGold;
          
          setGlobalStats({
            totalOres: {
              copper: totalCopper,
              silver: totalSilver,
              gold: totalGold,
              total: totalOresSum
            },
            totalCoins: data.totalCoins,
            totalClaimedCoins: data.totalClaimedCoins
          });
        } catch (error) {
          console.error('Failed to fetch global stats:', error);
        }
        
        await refetch();
        setActionError(null);
        setSmeltingActionLoading(false);
      }, 15000);
    }
  }, [isWriteLoading, writeData, transactionError]);
  
  // Handle successful transaction confirmation
  useEffect(() => {
    // Check if transaction was successful
    if (isTransactionSuccess && writeData) {
      console.log('Transaction confirmed successfully');
      setActionError(null);
      setSmeltingActionLoading(false);
      
      // Refresh all data
      const refreshAllData = async () => {
        await fetchSmeltingData();
        await refetch();
        
        // Refresh global stats
        try {
          const data = await miningApi.getGlobalStats();
          
          // Calculate total ores
          const totalCopper = data.smeltingOres.copper + data.smeltedOres.copper;
          const totalSilver = data.smeltingOres.silver + data.smeltedOres.silver;
          const totalGold = data.smeltingOres.gold + data.smeltedOres.gold;
          const totalOresSum = totalCopper + totalSilver + totalGold;
          
          setGlobalStats({
            totalOres: {
              copper: totalCopper,
              silver: totalSilver,
              gold: totalGold,
              total: totalOresSum
            },
            totalCoins: data.totalCoins,
            totalClaimedCoins: data.totalClaimedCoins
          });
        } catch (error) {
          console.error('Failed to fetch global stats:', error);
        }
      };
      
      refreshAllData();
    }
  }, [isTransactionSuccess, writeData]);
  
  const openClaimModal = () => {
    setShowClaimModal(true);
  };

  // Close claim modal
  const closeClaimModal = () => {
    setShowClaimModal(false);
    setClaimAmount('');
  };

  // Fetch global stats
  const fetchGlobalStats = async () => {
    try {
      setActionError(null);
      const data = await miningApi.getGlobalStats();
      
      // Calculate total ores by type (smelting + smelted)
      const totalCopper = data.smeltingOres.copper + data.smeltedOres.copper;
      const totalSilver = data.smeltingOres.silver + data.smeltedOres.silver;
      const totalGold = data.smeltingOres.gold + data.smeltedOres.gold;
      const totalOresSum = totalCopper + totalSilver + totalGold;
      
      setGlobalStats({
        totalOres: {
          copper: totalCopper,
          silver: totalSilver,
          gold: totalGold,
          total: totalOresSum
        },
        totalCoins: data.totalCoins,
        totalClaimedCoins: data.totalClaimedCoins
      });
    } catch (error: any) {
      console.error('Failed to fetch global stats:', error);
      setActionError(error.response?.data?.message || 'Failed to fetch global stats');
    }
  };

  // Load smelting data and global stats on component mount
  useEffect(() => {
    fetchSmeltingData();
    fetchGlobalStats();
  }, []);

  return (
    <DashboardLayout>
      {/* Upgrade Modal */}
      {showUpgradeModal && selectedMining && upgradeType && (
        <UpgradeModal
          isOpen={showUpgradeModal}
          onClose={closeUpgradeModal}
          onConfirm={handleUpgradeConfirm}
          upgradeType={upgradeType}
          selectedMining={selectedMining}
          isUpgrading={isUpgrading}
          upgradeError={upgradeError}
        />
      )}

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        message={successMessage}
      />

      <SuccessModal
        isOpen={showSmeltSuccessModal}
        onClose={() => setShowSmeltSuccessModal(false)}
        message="Ores sent for smelting! They'll be ready to claim as tokens once complete."
      />

      <ClaimModal
        isOpen={showClaimModal}
        onClose={closeClaimModal}
        onConfirm={handleClaimTokens}
        totalCoins={smeltingData.totalCoins}
        claimAmount={claimAmount}
        onClaimAmountChange={setClaimAmount}
        isLoading={smeltingActionLoading}
      />
      
      {/* Miners Grid Modal */}
      <MinersGridModal
        isOpen={showMinersGridModal}
        onClose={() => setShowMinersGridModal(false)}
        miners={minings}
        miningActionLoading={miningActionLoading}
        onStartMining={handleStartMining}
        onStopMining={handleStopMining}
        onStackMining={handleStackMining}
        isMiningInProgress={isMiningInProgress}
        getMiningStatusText={getMiningStatusText}
        countdowns={countdowns}
      />

      <ErrorModal
        isOpen={!!actionError}
        onClose={clearActionError}
        message={actionError || ''}
      />

      {isLoading ? (
        <div className="fixed inset-0 flex items-center justify-center -mt-24">
          <div className="text-white pixelated-text">Loading...</div>
        </div>
      ) : error ? (
        <ErrorModal
          isOpen={!!error}
          onClose={() => null}
          message={error}
          retryAction={() => refetch()}
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <ResourcesPanel
            stacks={stacks}
            globalStats={globalStats}
          />

          {/* Miners section */}
          <div className="bg-[#191919] rounded-lg p-6 shadow-lg md:col-span-3">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl">YOUR MINERS</h2>
              
              <div className="flex gap-2">
                <button
                  className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm flex items-center gap-1"
                  onClick={() => setShowMinersGridModal(true)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                  </svg>
                  View All Miners
                </button>
                
                <button
                  className={`px-3 py-1 rounded text-sm flex items-center gap-1 transition-all duration-200 ${
                    !minings.some(mining => mining.mining.status === 'READY_TO_SMELT') || Object.values(miningActionLoading).some(isLoading => isLoading)
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : 'bg-yellow-600 hover:bg-yellow-700 text-white'
                  }`}
                  onClick={handleStackAllMinings}
                  disabled={!minings.some(mining => mining.mining.status === 'READY_TO_SMELT') || Object.values(miningActionLoading).some(isLoading => isLoading)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                  Bulk Harvest
                </button>
              </div>
            </div>

            {minings.length > 0 ? (
              <Slider {...sliderSettings}>
                {[...minings, { id: 'empty', mining: { status: '', copper: 0, silver: 0, gold: 0 }, bag: { slots: 0 } }].map((mining) => (
                  mining.id === 'empty' ? (
                    <EmptyMinerCard key="empty" />
                  ) : (
                    <div 
                      key={mining.id} 
                      className="bg-[#222222] rounded-lg p-4 shadow-lg outline-none focus:outline-none h-full"
                    >
                      <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center gap-2">
                          <img 
                            src={getMinerProfilePicture(mining.miner.name)} 
                            alt={mining.miner.name} 
                            className="w-8 h-8 object-contain" 
                          />
                          <span className="pixelated-text font-bold">{mining.miner.name}</span>
                        </div>
                        <span className={`text-xs px-2 py-1 rounded-full text-black pixelated-text ${mining.mining.status === 'READY_TO_SMELT' ? 'bg-yellow-500' : isMiningInProgress(mining) ? 'bg-green-500' : 'bg-gray-500'}`}>
                          {getMiningStatusText(mining)}
                        </span>
                      </div>

                      <div className="flex flex-col gap-1 mb-3 text-xs pixelated-text">
                        {/* Bag info with upgrade button */}
                        <div className="flex justify-between items-center">
                          <span className="text-gray-400">Bag:</span>
                          <div className="flex items-center gap-1">
                            <img 
                              src={getBagImage(mining.bag.id)} 
                              alt="Bag" 
                              className="w-5 h-5 object-contain" 
                            />
                            <span className="text-white">{getBagDetails(mining.bag.id).name} ({mining.bag.slots} slots)</span>
                            {parseInt(mining.bag.id) < 4 && (
                              <div className="tooltip-container">
                                <button
                                  className={`w-5 h-5 flex items-center justify-center transition-all duration-200 ${
                                    isMiningInProgress(mining)
                                      ? 'opacity-40 cursor-not-allowed'
                                      : 'hover:opacity-80'
                                  }`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (!isMiningInProgress(mining)) {
                                      setSelectedMining(mining);
                                      setUpgradeType('bag');
                                      setShowUpgradeModal(true);
                                    }
                                  }}
                                  disabled={isMiningInProgress(mining)}
                                >
                                  <img src={upgradeIcon} alt="Upgrade bag" className="w-full h-full object-contain" />
                                </button>
                                <div className="tooltip">
                                  {isMiningInProgress(mining)
                                    ? 'Cannot upgrade bag while mining is in progress'
                                    : 'Upgrade bag to increase storage capacity'
                                  }
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Pickaxe info with upgrade button */}
                        <div className="flex justify-between items-center">
                          <span className="text-gray-400">Pickaxe:</span>
                          <div className="flex items-center gap-1">
                            <img 
                              src={getPickaxeImage(mining.pickaxe.id)} 
                              alt="Pickaxe" 
                              className="w-5 h-5 object-contain" 
                            />
                            <span className="text-white">{getPickaxeDetails(mining.pickaxe.id).name}</span>
                            {parseInt(mining.pickaxe.id) < 5 && (
                              <div className="tooltip-container">
                                <button
                                  className={`w-5 h-5 flex items-center justify-center transition-all duration-200 ${
                                    isMiningInProgress(mining)
                                      ? 'opacity-40 cursor-not-allowed'
                                      : 'hover:opacity-80'
                                  }`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (!isMiningInProgress(mining)) {
                                      setSelectedMining(mining);
                                      setUpgradeType('pickaxe');
                                      setShowUpgradeModal(true);
                                    }
                                  }}
                                  disabled={isMiningInProgress(mining)}
                                >
                                  <img src={upgradeIcon} alt="Upgrade pickaxe" className="w-full h-full object-contain" />
                                </button>
                                <div className="tooltip">
                                  {isMiningInProgress(mining)
                                    ? 'Cannot upgrade pickaxe while mining is in progress'
                                    : 'Upgrade pickaxe to increase mining speed and ore bonuses'
                                  }
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Speed info */}
                        <div className="flex justify-between">
                          <span className="text-gray-400">Mining Speed:</span>
                          <span className="text-green-400">{mining.pickaxe.speed}x</span>
                        </div>

                        {/* Ore bonuses if any */}
                        <div className="flex justify-between h-5">
                          <span className="text-gray-400">Ore Bonuses:</span>
                          <span className="text-yellow-400">
                            {mining.pickaxe.oreBonus.copper > 0 ? `+${mining.pickaxe.oreBonus.copper}% Cu ` : ''}
                            {mining.pickaxe.oreBonus.silver > 0 ? `+${mining.pickaxe.oreBonus.silver}% Ag ` : ''}
                            {mining.pickaxe.oreBonus.gold > 0 ? `+${mining.pickaxe.oreBonus.gold}% Au` : '-'}
                          </span>
                        </div>
                      </div>

                      {/* Timer countdown */}
                      <div className="flex justify-between mb-3 text-xs pixelated-text">
                        <span className="text-gray-400">Time remaining:</span>
                        <span className="text-green-400">{countdowns[mining.id] || '-'}</span>
                      </div>

                      {/* Miner character image based on status */}
                      <div className="flex justify-center mb-4">
                        <img 
                          src={getMinerImage(mining.miner.name, isMiningInProgress(mining))} 
                          alt={`${mining.miner.name} ${isMiningInProgress(mining) ? 'Mining' : 'Idle'}`} 
                          className="h-40 object-contain" 
                        />
                      </div>
                      
                      {/* Resources display */}
                      <div className="bg-[#191919] p-3 rounded-lg mb-4">
                        <div className="grid grid-cols-3 gap-2">
                          {/* Copper */}
                          <div className="flex flex-col items-center">
                            <img src={copperImage} alt="Copper" className="w-8 h-8 mb-1" />
                            <span className="pixelated-text text-xs">{mining.mining.copper}</span>
                          </div>
                          
                          {/* Silver */}
                          <div className="flex flex-col items-center">
                            <img src={silverImage} alt="Silver" className="w-8 h-8 mb-1" />
                            <span className="pixelated-text text-xs">{mining.mining.silver}</span>
                          </div>
                          
                          {/* Gold */}
                          <div className="flex flex-col items-center">
                            <img src={goldImage} alt="Gold" className="w-8 h-8 mb-1" />
                            <span className="pixelated-text text-xs">{mining.mining.gold}</span>
                          </div>
                        </div>
                        
                        {/* Bag capacity indicator */}
                        <div className="mt-2 bg-[#333333] h-2 rounded-full overflow-hidden">
                          <div 
                            className="bg-green-500 h-full" 
                            style={{ width: `${((mining.mining.copper + mining.mining.silver + mining.mining.gold) / mining.bag.slots) * 100}%` }}
                          ></div>
                        </div>
                        <div className="text-xs pixelated-text text-center mt-1 text-gray-400">
                          {mining.mining.copper + mining.mining.silver + mining.mining.gold} / {mining.bag.slots} slots
                        </div>
                      </div>

                      <div className="flex justify-between">
                        <button
                          className={`px-3 py-1 rounded text-sm pixelated-text ${isMiningInProgress(mining) ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'}`}
                          onClick={() => isMiningInProgress(mining) ? handleStopMining(mining.id) : handleStartMining(mining.id)}
                          disabled={miningActionLoading[mining.id] || false}
                        >
                          {miningActionLoading[mining.id] ? 'Processing...' : isMiningInProgress(mining) ? 'Stop Mining' : 'Start Mining'}
                        </button>

                        <button
                          className={`px-3 py-1 rounded text-sm pixelated-text transition-all duration-200 ${
                            (mining.mining.copper + mining.mining.silver + mining.mining.gold) === 0 || mining.mining.status !== 'READY_TO_SMELT' || miningActionLoading[mining.id]
                              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                              : 'bg-yellow-600 hover:bg-yellow-700 text-white'
                          }`}
                          onClick={() => handleStackMining(mining.id)}
                          disabled={(mining.mining.copper + mining.mining.silver + mining.mining.gold) === 0 || mining.mining.status !== 'READY_TO_SMELT' || miningActionLoading[mining.id]}
                        >
                          {miningActionLoading[mining.id] ? 'Processing...' : 'Harvest'}
                        </button>
                      </div>
                    </div>
                  )
                ))}
              </Slider>
            ) : (
              <div className="bg-[#222222] p-6 rounded-lg text-center">
                <p className="text-white pixelated-text">No miners available</p>
                <p className="text-gray-400 pixelated-text text-sm mt-2">Visit the store to buy your first miner</p>
                <a 
                  href="/store"
                  className="inline-block bg-[#432433] hover:bg-[#532d3e] text-white pixelated-text py-2 px-4 rounded mt-4"
                >
                  Go to Store
                </a>
              </div>
            )}
          </div>

          {/* Smelting & Tokens section */}
          <div className="bg-[#191919] rounded-lg p-6 shadow-lg md:col-span-4 mt-8">
            <h2 className="text-xl pixelated-text mb-4">SMELTING & TOKENS</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <SmeltingPanel
                stacks={stacks}
                smeltingActionLoading={smeltingActionLoading}
                
                onSmeltAllStacks={handleSmeltAllStacks}
              />

              <TokenPanel
                smeltingData={smeltingData}
                smeltingActionLoading={smeltingActionLoading}
                onClaimClick={openClaimModal}
              />
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
