import { chainConfig } from '../../op-stack/chainConfig.js';
import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
const sourceId = 1; // mainnet
export const mode = /*#__PURE__*/ defineChain({
    ...chainConfig,
    id: 34443,
    name: 'Mode Mainnet',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://mainnet.mode.network'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Modescan',
            url: 'https://modescan.io',
        },
    },
    contracts: {
        ...chainConfig.contracts,
        multicall3: {
            address: '******************************************',
            blockCreated: 2465882,
        },
        l2OutputOracle: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
            },
        },
    },
    sourceId,
});
//# sourceMappingURL=mode.js.map