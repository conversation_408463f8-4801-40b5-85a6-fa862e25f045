import { useState, useEffect } from 'preact/hooks';
import { useAccount, useWriteContract, useWaitForTransactionReceipt } from 'wagmi';
import storeApi from '../services/store';

// Import the payment gateway ABI directly
const paymentGatewayAbi = [
  // payWithToken function from the ABI
  {
    "inputs": [
      {
        "internalType": "string",
        "name": "orderId",
        "type": "string"
      },
      {
        "internalType": "uint256",
        "name": "amount",
        "type": "uint256"
      },
      {
        "internalType": "address",
        "name": "token",
        "type": "address"
      },
      {
        "internalType": "address",
        "name": "merchant",
        "type": "address"
      },
      {
        "internalType": "bytes",
        "name": "signature",
        "type": "bytes"
      }
    ],
    "name": "payWithToken",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

// ERC20 token ABI for approval
const erc20Abi = [
  // approve function
  {
    "inputs": [
      {
        "internalType": "address",
        "name": "spender",
        "type": "address"
      },
      {
        "internalType": "uint256",
        "name": "amount",
        "type": "uint256"
      }
    ],
    "name": "approve",
    "outputs": [
      {
        "internalType": "bool",
        "name": "",
        "type": "bool"
      }
    ],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];



// Get the contract address from environment variable
const PAYMENT_GATEWAY_ADDRESS = import.meta.env.VITE_PAYMENT_GATEWAY_ADDRESS;

interface Miner {
  id: number;
  name: string;
  luckLevel: string;
  speedLevel: string;
  price: number;
}

interface UseStoreReturn {
  miners: Miner[];
  isLoading: boolean;
  error: string | null;
  purchaseMiner: (minerId: number) => Promise<void>;
  isPurchasing: boolean;
  purchaseError: string | null;
  purchaseSuccess: boolean;
  refetch: () => Promise<void>;
}

export function useStore(): UseStoreReturn {
  const [miners, setMiners] = useState<Miner[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [purchaseError, setPurchaseError] = useState<string | null>(null);
  const [purchaseSuccess, setPurchaseSuccess] = useState(false);
  const { address } = useAccount();

  // Contract write hook for token payment
  const { writeContract, isPending: isWriteLoading, data: writeData } = useWriteContract();

  // Wait for transaction to complete
  const { isLoading: isTransactionLoading, error: transactionError, isSuccess: isTransactionSuccess } = useWaitForTransactionReceipt({
    hash: writeData,
  });

  // Handle transaction success and error
  useEffect(() => {
    if (writeData) {
      if (isTransactionSuccess) {
        // Transaction completed successfully
        console.log('Transaction confirmed successfully');
        setPurchaseError(null); // Clear any waiting messages
        setPurchaseSuccess(true);
        fetchMiners(); // Refresh miners after successful purchase
      } else if (!isTransactionLoading && !transactionError) {
        // Transaction is no longer loading but not yet marked as success
        // This could be a transitional state, so we'll log it
        console.log('Transaction no longer loading but not yet marked as success');

        // Check if we should force success based on elapsed time
        const transactionTime = localStorage.getItem('transactionSubmitTime');
        if (transactionTime && purchaseError === 'Purchase transaction submitted. Waiting for blockchain confirmation...') {
          const currentTime = new Date().getTime();
          const elapsedTime = currentTime - parseInt(transactionTime);
          // If it's been more than 5 seconds since submission and transaction is no longer loading,
          // assume it's successful even if not explicitly marked as success
          if (elapsedTime > 5000) {
            console.log('Forcing transaction success based on elapsed time in success handler');
            setPurchaseError(null);
            setPurchaseSuccess(true);
          }
        }
      }
    }
  }, [writeData, isTransactionLoading, transactionError, isTransactionSuccess, purchaseError]);

  // Additional effect to ensure purchaseError is cleared when transaction is confirmed
  useEffect(() => {
    if (purchaseSuccess) {
      setPurchaseError(null);
    }
  }, [purchaseSuccess]);

  // Handle transaction errors and check for potential success
  useEffect(() => {
    if (transactionError) {
      console.error('Transaction error:', transactionError);
      setPurchaseError('Transaction failed. Please try again.');
    } else if (writeData && !isTransactionLoading && !isTransactionSuccess) {
      // If we have transaction data, it's not loading, and not explicitly marked as success,
      // check if the error message is still showing the waiting message
      console.log('Checking for potential success in error handler');
      if (purchaseError === 'Purchase transaction submitted. Waiting for blockchain confirmation...') {
        // It's possible the transaction succeeded but wasn't properly detected
        // Check how long it's been since the transaction was submitted
        const currentTime = new Date().getTime();
        const transactionTime = localStorage.getItem('transactionSubmitTime');

        if (transactionTime) {
          const elapsedTime = currentTime - parseInt(transactionTime);
          // If it's been more than 10 seconds, assume success
          if (elapsedTime > 10000) {
            console.log('Transaction likely succeeded based on elapsed time');
            setPurchaseError(null);
            setPurchaseSuccess(true);
          }
        }
      }
    }
  }, [transactionError, writeData, isTransactionLoading, isTransactionSuccess, purchaseError]);

  // Polling mechanism to periodically check transaction status
  useEffect(() => {
    // Only run this effect if we're waiting for transaction confirmation
    if (purchaseError === 'Purchase transaction submitted. Waiting for blockchain confirmation...' && writeData) {
      console.log('Setting up polling for transaction status');

      // Check transaction status immediately when this effect runs
      const checkTransactionStatus = () => {
        console.log('Checking transaction status');

        // Check if transaction has been pending for too long
        const transactionTime = localStorage.getItem('transactionSubmitTime');
        if (transactionTime) {
          const currentTime = new Date().getTime();
          const elapsedTime = currentTime - parseInt(transactionTime);

          // If it's been more than 15 seconds, force success
          if (elapsedTime > 15000) {
            console.log('Forcing transaction success after timeout');
            setPurchaseError(null);
            setPurchaseSuccess(true);
            return true; // Return true to indicate we've forced success
          }
        }
        return false; // Return false to indicate we haven't forced success
      };

      // Check immediately when the effect runs
      if (checkTransactionStatus()) {
        // If we've already forced success, no need to set up the interval
        return;
      }

      // Set up polling interval to check transaction status every 3 seconds
      const pollingInterval = setInterval(() => {
        console.log('Polling transaction status');
        if (checkTransactionStatus()) {
          // If we've forced success, clear the interval
          clearInterval(pollingInterval);
        }
      }, 3000); // Check every 3 seconds

      // Clean up the interval when the component unmounts or when we're no longer waiting
      return () => {
        console.log('Cleaning up polling interval');
        clearInterval(pollingInterval);
      };
    }
  }, [purchaseError, writeData]);

  // Fetch all available miners
  const fetchMiners = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const minersData = await storeApi.getAllMiners();
      setMiners(minersData);
    } catch (error: any) {
      console.error('Error fetching miners:', error);
      setError(error.message || 'Failed to fetch miners');
    } finally {
      setIsLoading(false);
    }
  };

  // Purchase a miner
  const purchaseMiner = async (minerId: number): Promise<void> => {
    if (!address) {
      setPurchaseError('Wallet not connected');
      return;
    }

    // Reset states
    setPurchaseError(null);
    setPurchaseSuccess(false);

    try {
      // STEP 1: Call the backend to get transaction details
      setPurchaseError('Preparing transaction...');
      const transactionData = await storeApi.buyMiner(minerId);

      // Extract transaction parameters
      const { arguments: args, signature } = transactionData;
      const { orderId, amount, token, merchant } = args;

      // Format parameters
      const formattedToken = token.toLowerCase() as `0x${string}`;
      const formattedMerchant = merchant.toLowerCase() as `0x${string}`;
      const formattedSignature = signature.startsWith('0x') ? signature : `0x${signature}`;
      const amountBigInt = BigInt(amount);

      // STEP 2: First approve the token spending
      setPurchaseError('Please approve token spending in your wallet...');

      // This will show the MetaMask popup for token approval
      await writeContract({
        address: formattedToken,
        abi: erc20Abi,
        functionName: 'approve',
        args: [
          PAYMENT_GATEWAY_ADDRESS,
          amountBigInt
        ]
      });

      // Wait a bit for the approval to be processed
      setPurchaseError('Token approval confirmed. Preparing purchase transaction...');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // STEP 3: Now call the payWithToken function
      setPurchaseError('Please confirm the purchase transaction in your wallet...');

      // This will show the MetaMask popup for the purchase
      await writeContract({
        address: PAYMENT_GATEWAY_ADDRESS,
        abi: paymentGatewayAbi,
        functionName: 'payWithToken',
        args: [
          orderId,
          amountBigInt,
          formattedToken,
          formattedMerchant,
          formattedSignature
        ]
      });

      // Transaction has been confirmed by the user in MetaMask but not yet by the blockchain
      setPurchaseError('Purchase transaction submitted. Waiting for blockchain confirmation...');

      // Store the transaction submission time in localStorage for elapsed time checks
      localStorage.setItem('transactionSubmitTime', new Date().getTime().toString());

      // STEP 4: Wait for the transaction confirmation
      // The success state will be set by the useWaitForTransactionReceipt hook
      // when the transaction is confirmed on the blockchain
      // We keep the "waiting for confirmation" message until the transaction is confirmed

      // We don't need to set success here as it will be handled by the useEffect hook
      // that watches for transaction confirmation

      // We'll refresh miners list in the useEffect hook after confirmation
    } catch (error: any) {
      console.error('Error in purchase process:', error);

      // Determine the appropriate error message
      let errorMessage = 'Failed to purchase miner';

      if (error.code === 4001) {
        // User rejected the transaction in MetaMask
        errorMessage = 'Transaction was rejected in your wallet';
      } else if (error.response?.data?.message) {
        // Backend API error
        errorMessage = error.response.data.message;
      } else if (error.message) {
        // General error with message
        errorMessage = error.message;
      }

      setPurchaseError(errorMessage);
    }
  };

  // Fetch miners on component mount and check for pending transactions
  useEffect(() => {
    fetchMiners();

    // Check if there's a pending transaction from a previous session
    const transactionTime = localStorage.getItem('transactionSubmitTime');
    if (transactionTime) {
      const currentTime = new Date().getTime();
      const elapsedTime = currentTime - parseInt(transactionTime);

      // If there's a transaction time in localStorage and it's been less than 5 minutes,
      // it might be a transaction that was submitted but the user navigated away or refreshed
      if (elapsedTime < 300000) { // 5 minutes
        console.log('Found pending transaction from previous session');

        // If it's been more than 30 seconds, assume it's completed
        if (elapsedTime > 30000) {
          console.log('Transaction from previous session likely completed, setting success');
          setPurchaseError(null);
          setPurchaseSuccess(true);
        } else {
          // Otherwise, set the waiting message so our polling mechanism can take over
          console.log('Transaction from previous session still pending, setting waiting message');
          setPurchaseError('Purchase transaction submitted. Waiting for blockchain confirmation...');
        }
      } else {
        // If it's been more than 5 minutes, clear the transaction time
        console.log('Clearing stale transaction time from localStorage');
        localStorage.removeItem('transactionSubmitTime');
      }
    }
  }, []);

  return {
    miners,
    isLoading,
    error,
    purchaseMiner,
    isPurchasing: isWriteLoading || isTransactionLoading,
    purchaseError,
    purchaseSuccess,
    refetch: fetchMiners
  };
}

export default useStore;
