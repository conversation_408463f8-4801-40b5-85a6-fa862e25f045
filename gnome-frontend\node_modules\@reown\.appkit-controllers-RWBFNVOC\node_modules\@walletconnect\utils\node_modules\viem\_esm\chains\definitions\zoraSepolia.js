import { chainConfig } from '../../op-stack/chainConfig.js';
import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
const sourceId = 11_155_111; // sepolia
export const zoraSepolia = /*#__PURE__*/ defineChain({
    ...chainConfig,
    id: 999999999,
    name: 'Zora Sepolia',
    network: 'zora-sepolia',
    nativeCurrency: {
        decimals: 18,
        name: 'Zora Sepolia',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: {
            http: ['https://sepolia.rpc.zora.energy'],
            webSocket: ['wss://sepolia.rpc.zora.energy'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Zora Sepolia Explorer',
            url: 'https://sepolia.explorer.zora.energy/',
            apiUrl: 'https://sepolia.explorer.zora.energy/api',
        },
    },
    contracts: {
        ...chainConfig.contracts,
        l2OutputOracle: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        multicall3: {
            address: '******************************************',
            blockCreated: 83160,
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
            },
        },
    },
    sourceId,
    testnet: true,
});
//# sourceMappingURL=zoraSepolia.js.map