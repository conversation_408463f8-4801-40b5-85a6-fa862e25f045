// Preact component
import { useEffect, useState } from 'preact/hooks';
import { ConnectWallet } from '../wallet/ConnectWallet';
import { useAuth } from '../../context/AuthContext';

export function Navbar() {
  const { user, logout } = useAuth();
  const [currentPath, setCurrentPath] = useState(window.location.pathname);

  // Update the current path when navigation occurs
  useEffect(() => {
    const handleRouteChange = () => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handleRouteChange);
    
    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  // Simple navigation function
  const navigate = (path: string) => {
    window.history.pushState({}, '', path);
    setCurrentPath(path);
    // Dispatch a custom event to notify the App component of the route change
    window.dispatchEvent(new Event('popstate'));
  };

  return (
    <nav className="bg-black/30 backdrop-blur-sm py-3 px-4 fixed top-0 left-0 right-0 z-50">
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center">
          <span className="text-2xl text-white font-bold pixelated-text mr-8">The Cave</span>
          
          <div className="hidden md:flex space-x-4">
            <button 
              onClick={() => navigate('/dashboard')}
              className={`pixelated-text px-3 py-1 rounded ${currentPath === '/dashboard' ? 'bg-[#432433] text-white' : 'text-gray-300 hover:text-white'}`}
            >
              Dashboard
            </button>
            <button 
              onClick={() => navigate('/store')}
              className={`pixelated-text px-3 py-1 rounded ${currentPath === '/store' ? 'bg-[#432433] text-white' : 'text-gray-300 hover:text-white'}`}
            >
              Store
            </button>
          </div>
        </div>
        
        {user ? (
          <div className="flex items-center gap-4">
            <span className="pixelated-text text-white hidden md:inline">{user?.wallet?.slice(0, 6)}...{user?.wallet?.slice(-4)}</span>
            <button 
              className="connect-wallet-btn rounded pixelated-text"
              onClick={logout}
            >
              Logout
            </button>
          </div>
        ) : (
          <ConnectWallet />
        )}
      </div>
      
      {/* Mobile navigation */}
      <div className="md:hidden flex justify-center mt-2 space-x-4">
        <button 
          onClick={() => navigate('/dashboard')}
          className={`pixelated-text px-3 py-1 rounded ${currentPath === '/dashboard' ? 'bg-[#432433] text-white' : 'text-gray-300 hover:text-white'}`}
        >
          Dashboard
        </button>
        <button 
          onClick={() => navigate('/store')}
          className={`pixelated-text px-3 py-1 rounded ${currentPath === '/store' ? 'bg-[#432433] text-white' : 'text-gray-300 hover:text-white'}`}
        >
          Store
        </button>
      </div>
    </nav>
  );
}
