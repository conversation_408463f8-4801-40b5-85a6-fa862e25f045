import { chainConfig } from '../../op-stack/chainConfig.js';
import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
const sourceId = 1; // Ethereum mainnet
export const redstone = defineChain({
    ...chainConfig,
    name: '<PERSON><PERSON>',
    id: 690,
    sourceId,
    nativeCurrency: { decimals: 18, name: '<PERSON>ther', symbol: 'ETH' },
    rpcUrls: {
        default: {
            http: ['https://rpc.redstonechain.com'],
            webSocket: ['wss://rpc.redstonechain.com'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Blockscout',
            url: 'https://explorer.redstone.xyz',
        },
    },
    contracts: {
        ...chainConfig.contracts,
        multicall3: {
            address: '******************************************',
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 19578329,
            },
        },
        l2OutputOracle: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 19578337,
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 19578331,
            },
        },
    },
});
//# sourceMappingURL=redstone.js.map