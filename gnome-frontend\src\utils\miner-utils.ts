// CDN URLs for miner images
// Gnome images
const gnomeStatic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/gnomestatic.png';
const gnomeMining = 'https://thecave.ams3.cdn.digitaloceanspaces.com/gnomemining.gif';

// Dwarf images
const dwarfStatic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/dwarfstatic.png';
const dwarfMining = 'https://thecave.ams3.cdn.digitaloceanspaces.com/dwarfmining.gif';

// Orc images
const orcStatic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/orcstatic.png';
const orcMining = 'https://thecave.ams3.cdn.digitaloceanspaces.com/orcmining.gif';

// Human images
const humanStatic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/Human-Static.png';
const humanMining = 'https://thecave.ams3.cdn.digitaloceanspaces.com/Human.gif';

// Zombie images
const zombieStatic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/zombiestatic.png';
const zombieMining = 'https://thecave.ams3.cdn.digitaloceanspaces.com/zombiemotion.gif';

// Frog images
const frogStatic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/frogstatic.png';
const frogMining = 'https://thecave.ams3.cdn.digitaloceanspaces.com/frogmotion.gif';

// Pengu images
const penguStatic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/pengustatic.png';
const penguMining = 'https://thecave.ams3.cdn.digitaloceanspaces.com/pengumotion.gif';

// Ghoul images
const ghoulStatic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/ghoulstatic.png';
const ghoulMining = 'https://thecave.ams3.cdn.digitaloceanspaces.com/ghouldmotion.gif';

// Wisp images
const wispStatic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/wispstatic.png';
const wispMining = 'https://thecave.ams3.cdn.digitaloceanspaces.com/wispmotion.gif';

// Profile Picture Assets from CDN
const gnomeProfilePic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/gnomeprofile.png';
const dwarfProfilePic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/dwarfprofile.png';
const orcProfilePic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/orcprofile.png';
const humanProfilePic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/Human.png';
const zombieProfilePic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/zombiepp.png';
const frogProfilePic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/frogpp.png';
const penguProfilePic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/pengupp.png';
const ghoulProfilePic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/ghoulpp.png';
const wispProfilePic = 'https://thecave.ams3.cdn.digitaloceanspaces.com/wisppp.png';

/**
 * Get the appropriate miner image based on miner type and mining status
 */
export const getMinerImage = (minerName: string, isInProgress: boolean) => {
  const name = minerName.toUpperCase();
  if (name === 'GNOME') {
    return isInProgress ? gnomeMining : gnomeStatic;
  } else if (name === 'DWARF') {
    return isInProgress ? dwarfMining : dwarfStatic;
  } else if (name === 'ORC') {
    return isInProgress ? orcMining : orcStatic;
  } else if (name === 'HUMAN') {
    return isInProgress ? humanMining : humanStatic;
  } else if (name === 'ZOMBIE') {
    return isInProgress ? zombieMining : zombieStatic;
  } else if (name === 'FROG') {
    return isInProgress ? frogMining : frogStatic;
  } else if (name === 'PENGU') {
    return isInProgress ? penguMining : penguStatic;
  } else if (name === 'GHOUL') {
    return isInProgress ? ghoulMining : ghoulStatic;
  } else if (name === 'WISP') {
    return isInProgress ? wispMining : wispStatic;
  }
  // Default to gnome if type is unknown
  return isInProgress ? gnomeMining : gnomeStatic;
};

/**
 * Get the appropriate miner profile picture based on miner type
 */
export const getMinerProfilePicture = (minerName: string) => {
  const name = minerName.toUpperCase();
  if (name === 'GNOME') {
    return gnomeProfilePic;
  } else if (name === 'DWARF') {
    return dwarfProfilePic;
  } else if (name === 'ORC') {
    return orcProfilePic;
  } else if (name === 'HUMAN') {
    return humanProfilePic;
  } else if (name === 'ZOMBIE') {
    return zombieProfilePic;
  } else if (name === 'FROG') {
    return frogProfilePic;
  } else if (name === 'PENGU') {
    return penguProfilePic;
  } else if (name === 'GHOUL') {
    return ghoulProfilePic;
  } else if (name === 'WISP') {
    return wispProfilePic;
  }
  // Default to gnome profile picture if type is unknown
  return gnomeProfilePic;
};
